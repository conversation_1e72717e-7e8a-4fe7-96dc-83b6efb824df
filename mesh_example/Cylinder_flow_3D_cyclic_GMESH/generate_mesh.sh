#!/bin/bash

# Script to generate optimized mesh with proper cyclic boundary conditions
# for 3D cylinder flow case using Gmesh

echo "=== Generating 3D Cylinder Flow Mesh with Gmesh ==="

# Clean previous mesh
echo "Cleaning previous mesh..."
rm -rf constant/polyMesh
rm -f cylinder_flow.msh

# Generate mesh with Gmesh
echo "Generating mesh with Gmesh..."
gmsh -3 cylinder_flow.geo -o cylinder_flow.msh

if [ ! -f "cylinder_flow.msh" ]; then
    echo "Error: Gmesh failed to generate mesh!"
    exit 1
fi

# Convert to OpenFOAM format
echo "Converting mesh to OpenFOAM format..."
gmshToFoam cylinder_flow.msh

# Check if conversion was successful
if [ ! -d "constant/polyMesh" ]; then
    echo "Error: gmshToFoam failed!"
    exit 1
fi

# Use createPatch to properly set up cyclic boundary conditions
echo "Setting up cyclic boundary conditions with createPatch..."
createPatch -overwrite

if [ $? -ne 0 ]; then
    echo "createPatch failed, trying manual fix..."
    python3 fix_cyclic.py
fi

# Check mesh quality
echo "Checking mesh quality..."
checkMesh

echo "=== Mesh generation completed ==="
echo "Mesh statistics:"
echo "- Check the output above for cell count"
echo "- front and back patches should now be cyclic"
echo "- cylinder should be wall type"

# Display boundary patch info
echo ""
echo "Boundary patches:"
grep -A 10 -B 2 "type.*cyclic\|type.*wall\|type.*patch" constant/polyMesh/boundary
