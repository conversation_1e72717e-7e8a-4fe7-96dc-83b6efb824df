// Gmsh script for 3D cylinder flow with cyclic boundary conditions
// Domain: x=[0, 2.5], y=[0, 0.41], z=[0, 0.41]
// Cylinder: center=(0.45, 0.205, 0.205), diameter=0.1

// Mesh parameters - optimized for ~30k cells total
lc_far = 0.25;      // Far field mesh size (very coarse)
lc_cylinder = 0.04; // Cylinder surface mesh size (coarser)
lc_wake = 0.12;     // Wake region mesh size (coarser)
lc_boundary_layer = 0.005; // First boundary layer thickness

// Geometry parameters
domain_x_min = 0.0;
domain_x_max = 2.5;
domain_y_min = 0.0;
domain_y_max = 0.41;
domain_z_min = 0.0;
domain_z_max = 0.41;

cylinder_center_x = 0.45;
cylinder_center_y = 0.205;
cylinder_center_z = 0.205;
cylinder_radius = 0.05;  // diameter = 0.1

// Create domain box points
Point(1) = {domain_x_min, domain_y_min, domain_z_min, lc_far};
Point(2) = {domain_x_max, domain_y_min, domain_z_min, lc_far};
Point(3) = {domain_x_max, domain_y_max, domain_z_min, lc_far};
Point(4) = {domain_x_min, domain_y_max, domain_z_min, lc_far};
Point(5) = {domain_x_min, domain_y_min, domain_z_max, lc_far};
Point(6) = {domain_x_max, domain_y_min, domain_z_max, lc_far};
Point(7) = {domain_x_max, domain_y_max, domain_z_max, lc_far};
Point(8) = {domain_x_min, domain_y_max, domain_z_max, lc_far};

// Create domain box lines (bottom face)
Line(1) = {1, 2};
Line(2) = {2, 3};
Line(3) = {3, 4};
Line(4) = {4, 1};

// Create domain box lines (top face)
Line(5) = {5, 6};
Line(6) = {6, 7};
Line(7) = {7, 8};
Line(8) = {8, 5};

// Create domain box lines (vertical edges)
Line(9) = {1, 5};
Line(10) = {2, 6};
Line(11) = {3, 7};
Line(12) = {4, 8};

// Create cylinder center points
Point(100) = {cylinder_center_x, cylinder_center_y, domain_z_min, lc_cylinder};
Point(101) = {cylinder_center_x, cylinder_center_y, domain_z_max, lc_cylinder};

// Create cylinder points on bottom face
Point(102) = {cylinder_center_x + cylinder_radius, cylinder_center_y, domain_z_min, lc_cylinder};
Point(103) = {cylinder_center_x, cylinder_center_y + cylinder_radius, domain_z_min, lc_cylinder};
Point(104) = {cylinder_center_x - cylinder_radius, cylinder_center_y, domain_z_min, lc_cylinder};
Point(105) = {cylinder_center_x, cylinder_center_y - cylinder_radius, domain_z_min, lc_cylinder};

// Create cylinder points on top face
Point(106) = {cylinder_center_x + cylinder_radius, cylinder_center_y, domain_z_max, lc_cylinder};
Point(107) = {cylinder_center_x, cylinder_center_y + cylinder_radius, domain_z_max, lc_cylinder};
Point(108) = {cylinder_center_x - cylinder_radius, cylinder_center_y, domain_z_max, lc_cylinder};
Point(109) = {cylinder_center_x, cylinder_center_y - cylinder_radius, domain_z_max, lc_cylinder};

// Create cylinder arcs on bottom face
Circle(20) = {102, 100, 103};
Circle(21) = {103, 100, 104};
Circle(22) = {104, 100, 105};
Circle(23) = {105, 100, 102};

// Create cylinder arcs on top face
Circle(24) = {106, 101, 107};
Circle(25) = {107, 101, 108};
Circle(26) = {108, 101, 109};
Circle(27) = {109, 101, 106};

// Create vertical lines connecting bottom and top cylinder
Line(28) = {102, 106};
Line(29) = {103, 107};
Line(30) = {104, 108};
Line(31) = {105, 109};

// Create curve loops for domain faces
Curve Loop(1) = {1, 2, 3, 4};           // bottom face (z=0)
Curve Loop(2) = {5, 6, 7, 8};           // top face (z=0.41)
Curve Loop(3) = {1, 10, -5, -9};        // bottomWall face (y=0)
Curve Loop(4) = {3, 12, -7, -11};       // topWall face (y=0.41)
Curve Loop(5) = {4, 9, -8, -12};        // inlet face (x=0)
Curve Loop(6) = {2, 11, -6, -10};       // outlet face (x=2.5)

// Create curve loops for cylinder
Curve Loop(7) = {20, 21, 22, 23};       // bottom cylinder
Curve Loop(8) = {24, 25, 26, 27};       // top cylinder

// Create surfaces
Plane Surface(1) = {1, 7};              // front face (z=0) with cylinder hole - cyclic
Plane Surface(2) = {2, 8};              // back face (z=0.41) with cylinder hole - cyclic
Plane Surface(3) = {3};                 // bottomWall face (y=0)
Plane Surface(4) = {4};                 // topWall face (y=0.41)
Plane Surface(5) = {5};                 // inlet face (x=0)
Plane Surface(6) = {6};                 // outlet face (x=2.5)

// Create cylinder surfaces using ruled surfaces
Curve Loop(9) = {20, 29, -24, -28};     // cylinder surface 1
Curve Loop(10) = {21, 30, -25, -29};    // cylinder surface 2
Curve Loop(11) = {22, 31, -26, -30};    // cylinder surface 3
Curve Loop(12) = {23, 28, -27, -31};    // cylinder surface 4

Surface(7) = {9};                       // cylinder surface 1
Surface(8) = {10};                      // cylinder surface 2
Surface(9) = {11};                      // cylinder surface 3
Surface(10) = {12};                     // cylinder surface 4

// Create surface loop for the volume
Surface Loop(1) = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};

// Create volume
Volume(1) = {1};

// Physical groups for boundary conditions
Physical Surface("inlet") = {5};
Physical Surface("outlet") = {6};
Physical Surface("bottomWall") = {3};   // bottomWall face (y=0)
Physical Surface("topWall") = {4};      // topWall face (y=0.41)
Physical Surface("front") = {1};       // front face (z=0) - cyclic
Physical Surface("back") = {2};        // back face (z=0.41) - cyclic
Physical Surface("cylinder") = {7, 8, 9, 10};

// Physical volume
Physical Volume("fluid") = {1};

// Mesh algorithm settings for polyhedral mesh
Mesh.Algorithm3D = 10;  // HXT algorithm for tetrahedral mesh
Mesh.ElementOrder = 1;  // Linear elements

// Cylinder surface refinement field
Field[1] = Distance;
Field[1].SurfacesList = {7, 8, 9, 10};  // Cylinder surfaces

// Boundary layer field around cylinder
Field[2] = Threshold;
Field[2].InField = 1;
Field[2].SizeMin = lc_boundary_layer;   // First layer thickness
Field[2].SizeMax = lc_cylinder;         // Max size near cylinder
Field[2].DistMin = 0.0;
Field[2].DistMax = cylinder_radius * 0.5; // Smaller transition zone

// Wake refinement field (smaller wake region)
Field[3] = Box;
Field[3].XMin = cylinder_center_x;
Field[3].XMax = cylinder_center_x + 0.3; // Much shorter wake region
Field[3].YMin = cylinder_center_y - 0.06;
Field[3].YMax = cylinder_center_y + 0.06;
Field[3].ZMin = domain_z_min;
Field[3].ZMax = domain_z_max;
Field[3].VIn = lc_wake;
Field[3].VOut = lc_far;

// Combine fields
Field[4] = Min;
Field[4].FieldsList = {2, 3, 5};
Background Field = 4;

// Mesh quality settings
Mesh.Optimize = 1;
Mesh.OptimizeNetgen = 1;
Mesh.HighOrderOptimize = 1;

// Boundary layer settings for cylinder (using Field approach)
Field[5] = BoundaryLayer;
Field[5].EdgesList = {20, 21, 22, 23, 24, 25, 26, 27}; // Cylinder edges
Field[5].hfar = lc_cylinder;
Field[5].hwall_n = lc_boundary_layer;
Field[5].ratio = 1.2;
Field[5].thickness = 0.01;
