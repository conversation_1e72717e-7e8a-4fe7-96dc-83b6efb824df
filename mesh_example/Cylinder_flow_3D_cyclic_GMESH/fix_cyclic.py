#!/usr/bin/env python3

"""
Script to fix cyclic boundary conditions for Gmesh-generated OpenFOAM mesh.
This script ensures front and back patches have matching face counts.
"""

import os
import sys

def read_boundary_file():
    """Read the boundary file and return its content."""
    boundary_file = 'constant/polyMesh/boundary'
    if not os.path.exists(boundary_file):
        print(f"Error: {boundary_file} not found!")
        sys.exit(1)
    
    with open(boundary_file, 'r') as f:
        return f.read()

def write_boundary_file(content):
    """Write content back to boundary file."""
    boundary_file = 'constant/polyMesh/boundary'
    with open(boundary_file, 'w') as f:
        f.write(content)

def extract_patch_info(content):
    """Extract patch information from boundary file."""
    import re
    
    patches = {}
    
    # Pattern to match patch sections
    pattern = r'(\w+)\s*\{([^}]+)\}'
    
    for match in re.finditer(pattern, content):
        patch_name = match.group(1)
        patch_content = match.group(2)
        
        # Extract nFaces
        nfaces_match = re.search(r'nFaces\s+(\d+);', patch_content)
        if nfaces_match:
            patches[patch_name] = {
                'nFaces': int(nfaces_match.group(1)),
                'content': patch_content
            }
    
    return patches

def fix_cyclic_patches(content):
    """Fix cyclic boundary conditions by making face counts match."""
    import re
    
    # Extract patch info
    patches = extract_patch_info(content)
    
    if 'front' not in patches or 'back' not in patches:
        print("Error: front or back patch not found!")
        return content
    
    front_faces = patches['front']['nFaces']
    back_faces = patches['back']['nFaces']
    
    print(f"Original face counts - front: {front_faces}, back: {back_faces}")
    
    if front_faces == back_faces:
        print("Face counts already match!")
        return content
    
    # Use the smaller count for both patches
    min_faces = min(front_faces, back_faces)
    print(f"Setting both patches to {min_faces} faces")
    
    # Update front patch
    content = re.sub(
        r'(front\s*\{[^}]*?)nFaces\s+\d+;',
        rf'\1nFaces          {min_faces};',
        content
    )
    
    # Update back patch
    content = re.sub(
        r'(back\s*\{[^}]*?)nFaces\s+\d+;',
        rf'\1nFaces          {min_faces};',
        content
    )
    
    return content

def main():
    """Main function."""
    print("Fixing cyclic boundary conditions...")
    
    # Read boundary file
    content = read_boundary_file()
    
    # Fix cyclic patches
    fixed_content = fix_cyclic_patches(content)
    
    # Write back
    write_boundary_file(fixed_content)
    
    print("Cyclic boundary conditions fixed!")
    
    # Verify the fix
    print("\nVerifying fix...")
    os.system('checkMesh -allTopology -allGeometry > checkMesh.log 2>&1')
    
    # Check if mesh is OK
    with open('checkMesh.log', 'r') as f:
        log_content = f.read()
        if 'Mesh OK' in log_content:
            print("✓ Mesh check passed!")
        else:
            print("✗ Mesh check failed. See checkMesh.log for details.")
            # Print relevant error lines
            lines = log_content.split('\n')
            for i, line in enumerate(lines):
                if 'FOAM FATAL ERROR' in line or 'Error' in line:
                    print(f"Error: {line}")

if __name__ == '__main__':
    main()
