/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2406                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      createPatchDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

pointSync false;

patches
(
    {
        name front;
        patchInfo
        {
            type cyclic;
            neighbourPatch back;
            matchTolerance 1e-3;
            transform translational;
            separationVector (0 0 -0.41);
        }
        constructFrom patches;
        patches (front);
    }

    {
        name back;
        patchInfo
        {
            type cyclic;
            neighbourPatch front;
            matchTolerance 1e-3;
            transform translational;
            separationVector (0 0 0.41);
        }
        constructFrom patches;
        patches (back);
    }

    {
        name cylinder;
        patchInfo
        {
            type wall;
        }
        constructFrom patches;
        patches (cylinder);
    }

    {
        name topWall;
        patchInfo
        {
            type wall;
        }
        constructFrom patches;
        patches (topWall);
    }

    {
        name bottomWall;
        patchInfo
        {
            type wall;
        }
        constructFrom patches;
        patches (bottomWall);
    }

    {
        name inlet;
        patchInfo
        {
            type patch;
        }
        constructFrom patches;
        patches (inlet);
    }

    {
        name outlet;
        patchInfo
        {
            type patch;
        }
        constructFrom patches;
        patches (outlet);
    }
);

// ************************************************************************* //
