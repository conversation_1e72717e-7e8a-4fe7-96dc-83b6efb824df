/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      fvSchemes;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

ddtSchemes
{
    default         backward;
}

gradSchemes
{
    default         Gauss linear corrected;
    grad(p)         Gauss linear corrected;
    grad(U)         Gauss linear corrected;
}

divSchemes
{
    default         none;
    div(phi,U)      Gauss linear
    // div(phi,U)      Gauss linear corrected;
    // div(phi,U)      Gauss limitedLinearV 1;
    // div(phi,U)      Gauss linearUpwind grad(U);
    // div(phi,U)      Gauss limitedLinearV 1;
    // div((nuEff*dev2(T(grad(U))))) Gauss linear corrected;
}

laplacianSchemes
{
    default         Gauss linear corrected;
}

interpolationSchemes
{
    default         linear;
}

snGradSchemes
{
    default         corrected;
}

//wallDist
//{
//   method meshWave;
//}

// ************************************************************************* //
