#!/bin/bash

echo "=== 清理旧网格 ==="
# 清理旧的网格和结果
rm -rf constant/polyMesh
rm -rf processor*
rm -rf [1-9]*
rm -rf 0.[1-9]*
find . -name "*.foam" -delete

echo "=== 生成背景网格 ==="
blockMesh
if [ $? -ne 0 ]; then
    echo "❌ blockMesh失败！"
    exit 1
fi

echo "=== 检查背景网格 ==="
checkMesh

echo "=== 运行snappyHexMesh ==="
snappyHexMesh -overwrite
if [ $? -ne 0 ]; then
    echo "❌ snappyHexMesh失败！"
    exit 1
fi

echo "=== 检查最终网格 ==="
checkMesh

echo "=== 转换为VTK格式 ==="
foamToVTK

echo "✅ 网格生成完成！"
echo "📁 VTK文件位置: VTK/"
echo "🔍 可以在ParaView中查看网格质量"
