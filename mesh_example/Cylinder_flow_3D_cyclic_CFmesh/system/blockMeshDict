/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

convertToMeters 1;

// Geometry parameters based on documentation
// Domain: x=[0, 2.5], y=[0, 0.41], z=[0, 0.41]
// Cylinder: center=(0.45, 0.205, 0.205), diameter=0.1
// Single block background mesh

vertices
(
    // Bottom face (z=0)
    (0.0    0.0     0.0)    // 0
    (2.5    0.0     0.0)    // 1
    (2.5    0.41    0.0)    // 2
    (0.0    0.41    0.0)    // 3

    // Top face (z=0.41)
    (0.0    0.0     0.41)   // 4
    (2.5    0.0     0.41)   // 5
    (2.5    0.41    0.41)   // 6
    (0.0    0.41    0.41)   // 7
);

blocks
(
    // Single background block covering entire domain
    hex (0 1 2 3 4 5 6 7) (120 50 4) simpleGrading (1 1 1)
);

edges
(
);

boundary
(
    inlet
    {
        type patch;
        faces
        (
            (0 4 7 3)
        );
    }

    outlet
    {
        type patch;
        faces
        (
            (1 2 6 5)
        );
    }

    topWall
    {
        type wall;
        faces
        (
            (3 7 6 2)
        );
    }

    bottomWall
    {
        type wall;
        faces
        (
            (0 1 5 4)
        );
    }

    front
    {
        type cyclic;
        neighbourPatch back;
        faces
        (
            (0 3 2 1)
        );
    }

    back
    {
        type cyclic;
        neighbourPatch front;
        faces
        (
            (4 5 6 7)
        );
    }
);

mergePatchPairs
(
);

// ************************************************************************* //
