/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | cfMesh: A library for mesh generation          |
|  \\    /   O peration     | pMesh - Polyhedral Mesh Generator              |
|   \\  /    A nd           | Author: <PERSON><PERSON><PERSON>                         |
|    \\/     M anipulation  | E-mail: <EMAIL>            |
\*---------------------------------------------------------------------------*/

FoamFile
{
    version   2.0;
    format    ascii;
    class     dictionary;
    location  "system";
    object    meshDict;
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

// 使用STL格式的表面网格文件
surfaceFile "constant/triSurface/combined_patches.stl";

// 基础网格尺寸 - 设置为全场统一的较小尺寸
maxCellSize 0.05;

// 确保网格覆盖完整域
keepCellsIntersectingBoundary 1;

// 局部细化设置
localRefinement
{
    // 圆柱表面细化
    cylinder
    {
        cellSize 0.015;
    }

    // 引用尾流区域细化
    // "wakeBox.*"
    // {
    //     cellSize 0.05;
    // }

}

// 几何体细化区域定义
// objectRefinements
// {
//     // 尾流区域方形细化
//     wakeBox
//     {
//         type box;
//         centre (1.5 0.205 0.205);
//         lengthX 2.15;
//         lengthY 0.175;
//         lengthZ 0.41;
//         cellSize 0.05;
//     }

// }

// 边界层生成 - 圆柱表面生成棱柱体边界层
boundaryLayers
{
    patchBoundaryLayers
    {
        cylinder
        {
            nLayers           2;              // 3层边界层
            thicknessRatio    1.2;            // 拉伸比
            maxFirstLayerThickness 0.0075;     // 第一层厚度
        }
    }
}

// 边界条件重命名
renameBoundary
{
    defaultName defaultFaces;
    defaultType wall;

    newPatchNames
    {
        "inlet"
        {
            newName inlet;
            newType patch;
        }
        "outlet"
        {
            newName outlet;
            newType patch;
        }
        "cylinder"
        {
            newName cylinder;
            newType wall;
        }
        "topWall"
        {
            newName topWall;
            newType wall;
        }
        "bottomWall"
        {
            newName bottomWall;
            newType wall;
        }
        "front"
        {
            newName front;
            newType cyclic;
            neighbourPatch back;
        }
        "back"
        {
            newName back;
            newType cyclic;
            neighbourPatch front;
        }
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
