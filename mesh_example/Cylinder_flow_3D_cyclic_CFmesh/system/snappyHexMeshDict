/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      snappyHexMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

// 网格质量控制参数 - 启用边界层生成以提高圆柱表面网格质量
castellatedMesh true;
snap            true;
addLayers       false;   // 启用边界层生成以改善圆柱表面网格质量

// STL几何文件定义
geometry
{
    cylinder_comsol_quad.stl
    {
        type triSurfaceMesh;
        name cylinder;
    }

    // 定义长方形加密区域
    //refinementBox
    //{
    //    type searchableBox;
    //    min (0.20 0.075 0);
    //    max (3.00 0.335 1);
    //}
}

// 网格细化参数 - 大幅减少单元数用于Re=20验证
castellatedMeshControls
{
    // 最大局部细化级别 - 大幅减少
    maxLocalCells 50000;

    // 最大全局细化级别 - 大幅减少
    maxGlobalCells 100000;

    // 最小细化级别
    minRefinementCells 10;

    // 最大不一致级别
    maxLoadUnbalance 0.10;

    // 缓冲层数量 - 增加以实现更平滑的过渡
    nCellsBetweenLevels 4;
    
    // 表面特征细化
    features
    (
    );
    
    // 表面细化级别 - 适度细化以保证质量
    refinementSurfaces
    {
        cylinder
        {
            level (1 2);  // 适度细化保证圆柱表面质量
            patchInfo { type wall; }
        }
    }
    
    // 特定表面patch类型覆盖
    resolveFeatureAngle 30;
    
    // 区域细化（在圆柱附近）- 移除距离加密，只使用长方形区域加密
    refinementRegions
    {
        cylinder
        {
            mode distance;     // 基于距离的细化
            levels ((0.1 1));  // 距离圆柱表面0.1m内细化2级
        }

    // 添加长方形加密区域，包围圆柱及其尾流区域
    //    refinementBox
    //    {
    //       mode inside;       // 在指定区域内部细化
    //       levels ((1e15 1)); // 在整个区域内细化1级，避免过度跳跃
    //   }
    }
    
    // 位置检测点 (确保在流体域内)
    locationInMesh (1.0 0.2 0.2);
    
    // 网格收敛性控制
    allowFreeStandingZoneFaces true;
}

// 网格贴合参数
snapControls
{
    // 贴合迭代次数 - 大幅增加以提高质量
    nSmoothPatch 20;

    // 网格质量控制的容忍度 - 适度放宽
    tolerance 1.5;

    // 解析特征的迭代次数 - 大幅增加
    nSolveIter 100;

    // 松弛迭代次数 - 大幅增加
    nRelaxIter 20;
    
    // 特征贴合
    nFeatureSnapIter 10;
    
    // 隐式特征贴合
    implicitFeatureSnap false;
    
    // 显式特征贴合
    explicitFeatureSnap true;
    
    // 多区域特征贴合
    multiRegionFeatureSnap false;
}

// *** 边界层网格生成控制参数 ***
addLayersControls
{
    // 使用相对尺寸
    relativeSizes true;
    
    // 指定需要生成边界层的表面
    layers
    {
        cylinder
        {
            nSurfaceLayers 5;        // 生成2层边界层网格
        }
    }

    // 边界层厚度配置 - 关键参数！（保守设置以确保成功生成）
    firstLayerThickness 0.5;     // 第一层厚度相对于当地网格尺寸的比例（保守设置）
    expansionRatio 1.05;          // 层间拉伸比率：每层厚度是前一层的1.2倍（保守设置）

    // 最小厚度限制
    minThickness 0.01;            // 最小边界层厚度相对于当地网格尺寸（保守设置）
    
    // 边界层生成控制
    nGrow 0;                    // 向外增长层数
    featureAngle 180;           // 特征角度阈值（放宽以适应圆柱曲面）
    slipFeatureAngle 10;        // 滑移特征角度

    // 迭代控制
    nRelaxIter 10;              // 松弛迭代次数（增加以提高质量）
    nSmoothSurfaceNormals 3;    // 表面法向量平滑次数（增加）
    nSmoothNormals 5;           // 法向量平滑次数（增加）
    nSmoothThickness 15;        // 厚度平滑次数（增加）

    // 质量控制参数（针对圆柱表面优化，进一步放宽）
    maxFaceThicknessRatio 0.9;      // 面厚度比率上限（进一步放宽）
    maxThicknessToMedialRatio 0.8;  // 厚度与中线距离比率上限（进一步放宽）
    minMedialAxisAngle 30;          // 最小中线轴角度（进一步降低以适应曲面）

    // 层生成迭代控制
    nLayerIter 100;             // 层生成迭代次数（增加）
    nRelaxedIter 50;            // 松弛迭代次数（增加）
    
    // 缓冲区控制
    nBufferCellsNoExtrude 0;    // 不挤出的缓冲单元数
}

// 网格质量控制 - 平衡质量和收敛性
meshQualityControls
{
    // 最大非正交性 - 适度放宽以确保收敛
    maxNonOrtho 65;

    // 最大边界偏斜度 - 更严格
    maxBoundarySkewness 15;

    // 最大内部偏斜度 - 更严格
    maxInternalSkewness 3;

    // 最大凹度 - 更严格
    maxConcave 60;
    
    // 最小体积
    minVol 1e-13;
    
    // 最小四面体质量
    minTetQuality 1e-30;
    
    // 最小面积
    minArea -1;
    
    // 最小扭曲度
    minTwist 0.05;
    
    // 最小确定性
    minDeterminant 0.001;
    
    // 最小面平整度
    minFaceWeight 0.05;
    
    // 最小体积比率
    minVolRatio 0.01;
    
    // 最小三角形扭曲度
    minTriangleTwist -1;
    
    // 快照网格质量的迭代次数
    nSmoothScale 4;
    
    // 错误减少
    errorReduction 0.75;
    
    // 松弛迭代次数 - 适度放宽但保持质量
    relaxed
    {
        maxNonOrtho 65;
        maxBoundarySkewness 20;
        maxInternalSkewness 5;
        maxConcave 75;
        minTetQuality 1e-30;
        minTwist 0.05;
        minFaceWeight 0.05;
        minVolRatio 0.01;
    }
}

// 调试开关
debug 0;

// 合并容忍度
mergeTolerance 1e-6;

// ************************************************************************* //
