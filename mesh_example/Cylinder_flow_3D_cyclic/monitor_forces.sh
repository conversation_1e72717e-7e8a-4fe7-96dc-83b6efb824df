#!/bin/bash

# =============================================================================
# 圆柱绕流力系数实时监控脚本
# 用法: ./monitor_forces.sh
# =============================================================================

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 监控文件路径
COEFF_FILE="postProcessing/forceCoeffs/0/coefficient.dat"
FORCE_FILE="postProcessing/forces/0/force.dat"
LOG_FILE="force_coefficients_monitor.log"

# 创建监控日志
echo "# 圆柱绕流力系数实时监控 - $(date)" > $LOG_FILE
echo "# Time(s)    Cd(阻力)    Cl(升力)    Cd_pressure    Cd_viscous    Fx(N)    Fy(N)" >> $LOG_FILE

print_header() {
    clear
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}   圆柱绕流力系数实时监控 (Re=100)   ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo
}

# 检查求解器是否在运行
check_solver() {
    if pgrep -f "icoFoam" > /dev/null; then
        return 0
    else
        return 1
    fi
}

# 主监控循环
monitor_loop() {
    local last_time=0
    local max_cd=0
    local min_cd=999
    local count=0
    
    while true; do
        print_header
        
        # 检查求解器状态
        if check_solver; then
            echo -e "${GREEN}✓ icoFoam求解器运行中...${NC}"
        else
            echo -e "${YELLOW}⚠ 未检测到icoFoam进程${NC}"
        fi
        
        echo
        echo "监控文件状态："
        if [ -f "$COEFF_FILE" ]; then
            echo -e "  ${GREEN}✓ 力系数文件: $COEFF_FILE${NC}"
            
            # 读取最新数据
            if [ -s "$COEFF_FILE" ]; then
                latest_line=$(tail -n 1 "$COEFF_FILE")
                
                # 检查是否为有效数据行
                if [[ $latest_line =~ ^[0-9] ]]; then
                    time=$(echo $latest_line | awk '{print $1}')
                    cd_total=$(echo $latest_line | awk '{print $2}')
                    cd_pressure=$(echo $latest_line | awk '{print $3}')
                    cl_total=$(echo $latest_line | awk '{print $4}')
                    cl_pressure=$(echo $latest_line | awk '{print $5}')
                    cd_viscous=$(echo $latest_line | awk '{printf "%.6f", $2-$3}')
                    
                    # 更新统计
                    if (( $(echo "$cd_total > $max_cd" | bc -l) )); then
                        max_cd=$cd_total
                    fi
                    if (( $(echo "$cd_total < $min_cd" | bc -l) )); then
                        min_cd=$cd_total
                    fi
                    count=$((count + 1))
                    
                    # 显示当前值
                    echo
                    echo "当前时刻: t = ${time}s"
                    echo "----------------------------------------"
                    printf "阻力系数 Cd     : %8.4f\n" $cd_total
                    printf "  - 压力贡献    : %8.4f\n" $cd_pressure
                    printf "  - 粘性贡献    : %8.4f\n" $cd_viscous
                    printf "升力系数 Cl     : %8.4f\n" $cl_total
                    printf "升力系数(压力)  : %8.4f\n" $cl_pressure
                    
                    echo
                    echo "统计信息:"
                    printf "  最大Cd: %6.4f  |  最小Cd: %6.4f  |  数据点: %d\n" $max_cd $min_cd $count
                    
                    # 检查力文件
                    if [ -f "$FORCE_FILE" ]; then
                        force_line=$(tail -n 1 "$FORCE_FILE")
                        if [[ $force_line =~ ^[0-9] ]]; then
                            fx=$(echo $force_line | awk '{print $2}' | tr -d '()')
                            fy=$(echo $force_line | awk '{print $3}' | tr -d '()')
                            
                            echo
                            printf "原始力值: Fx = %8.4f N,  Fy = %8.4f N\n" $fx $fy
                            
                            # 写入日志（只在时间更新时写入）
                            if (( $(echo "$time > $last_time" | bc -l) )); then
                                printf "%8.3f  %10.6f  %10.6f  %10.6f  %10.6f  %10.6f  %10.6f\n" \
                                    $time $cd_total $cl_total $cd_pressure $cd_viscous $fx $fy >> $LOG_FILE
                                last_time=$time
                            fi
                        fi
                    fi
                    
                else
                    echo "  等待有效数据..."
                fi
            else
                echo "  文件为空，等待数据..."
            fi
        else
            echo -e "  ${YELLOW}⚠ 力系数文件不存在: $COEFF_FILE${NC}"
        fi
        
        if [ -f "$FORCE_FILE" ]; then
            echo -e "  ${GREEN}✓ 原始力文件: $FORCE_FILE${NC}"
        else
            echo -e "  ${YELLOW}⚠ 原始力文件不存在: $FORCE_FILE${NC}"
        fi
        
        echo
        echo "日志文件: $LOG_FILE"
        echo
        echo -e "${YELLOW}按 Ctrl+C 停止监控${NC}"
        
        # 等待5秒
        sleep 5
    done
}

# 信号处理
cleanup() {
    echo
    echo -e "${GREEN}监控已停止。数据已保存至: $LOG_FILE${NC}"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 启动监控
echo "启动圆柱绕流力系数监控..."
echo "监控间隔: 5秒"
echo "按 Ctrl+C 停止监控"
echo

monitor_loop
