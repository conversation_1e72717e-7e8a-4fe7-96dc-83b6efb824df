#!/bin/bash

# =============================================================================
# 3D圆柱绕流自动化仿真脚本 (Re=100)
# 功能：网格生成 -> 质量检查 -> 求解 -> 力系数监控
# =============================================================================

# 设置OpenFOAM环境
source /usr/lib/openfoam/openfoam2406/etc/bashrc

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}============================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# 检查OpenFOAM环境
check_openfoam() {
    if ! command -v blockMesh &> /dev/null; then
        print_error "OpenFOAM环境未正确加载"
        exit 1
    fi
    print_success "OpenFOAM环境检查通过"
}

# 步骤1：检查并生成网格
generate_mesh() {
    print_header "步骤1：网格生成检查"

    # 检查网格文件是否存在且完整
    if [ -d "constant/polyMesh" ] && [ -f "constant/polyMesh/points" ] && \
       [ -f "constant/polyMesh/faces" ] && [ -f "constant/polyMesh/owner" ] && \
       [ -f "constant/polyMesh/neighbour" ] && [ -f "constant/polyMesh/boundary" ]; then

        # 检查网格文件是否为空
        if [ -s "constant/polyMesh/points" ] && [ -s "constant/polyMesh/faces" ]; then
            print_success "检测到完整的网格文件，跳过网格生成"
            return 0
        else
            print_warning "检测到空的网格文件，将重新生成网格"
            rm -rf constant/polyMesh
        fi
    fi

    print_success "开始生成基础网格 (blockMesh)..."
    if ! blockMesh > log.blockMesh 2>&1; then
        print_error "blockMesh失败，请检查log.blockMesh"
        exit 1
    fi
    print_success "基础网格生成完成"

    print_success "开始生成精细网格 (snappyHexMesh)..."
    if ! snappyHexMesh -overwrite > log.snappyHexMesh 2>&1; then
        print_error "snappyHexMesh失败，请检查log.snappyHexMesh"
        exit 1
    fi
    print_success "精细网格生成完成"
}

# 步骤2：网格质量检查
check_mesh_quality() {
    print_header "步骤2：网格质量检查"
    
    if ! checkMesh > log.checkMesh 2>&1; then
        print_error "网格质量检查失败，请检查log.checkMesh"
        exit 1
    fi
    
    # 提取关键质量指标
    cells=$(grep "cells:" log.checkMesh | awk '{print $2}')
    max_nonortho=$(grep "Max.*non-orthogonality" log.checkMesh | awk '{print $3}')
    max_skewness=$(grep "Max skewness" log.checkMesh | awk '{print $4}')
    
    echo "网格统计信息："
    echo "  - 网格单元数: $cells"
    echo "  - 最大非正交性: $max_nonortho°"
    echo "  - 最大偏斜度: $max_skewness"
    
    if grep -q "Mesh OK" log.checkMesh; then
        print_success "网格质量检查通过"
    else
        print_error "网格质量不合格"
        exit 1
    fi
}

# 步骤3：执行求解并监控
run_simulation() {
    print_header "步骤3：开始CFD求解 (Re=100)"
    
    # 验证雷诺数设置
    U=$(grep "uniform.*(" 0/U | head -1 | awk '{print $3}' | tr -d '(')
    nu=$(grep "nu.*\[" constant/transportProperties | awk '{print $NF}' | tr -d ';')
    D=0.1  # 圆柱直径
    Re=$(echo "scale=2; $U * $D / $nu" | bc -l)
    
    echo "当前仿真参数："
    echo "  - 入口速度 U = $U m/s"
    echo "  - 运动粘度 ν = $nu m²/s"
    echo "  - 圆柱直径 D = $D m"
    echo "  - 雷诺数 Re = $Re"
    
    if (( $(echo "$Re < 95 || $Re > 105" | bc -l) )); then
        print_warning "雷诺数偏离目标值100，当前Re=$Re"
    else
        print_success "雷诺数设置正确 (Re=$Re)"
    fi
    
    # 清理之前的结果
    rm -rf [1-9]* processor* postProcessing
    
    # 创建监控日志文件
    monitor_log="force_coefficients_monitor.log"
    echo "# 圆柱绕流力系数监控 (Re=$Re)" > $monitor_log
    echo "# Time(s)  Cd(阻力系数)  Cl(升力系数)  Pressure_Cd  Viscous_Cd" >> $monitor_log
    
    print_success "开始icoFoam求解..."
    
    # 后台运行求解器
    icoFoam > log.icoFoam 2>&1 &
    solver_pid=$!
    
    print_success "求解器已启动 (PID: $solver_pid)"
    print_success "开始监控力系数..."
    
    # 监控循环
    while kill -0 $solver_pid 2>/dev/null; do
        sleep 5
        
        # 检查是否有新的力系数数据
        if [ -f "postProcessing/forceCoeffs/0/coefficient.dat" ]; then
            # 获取最新的力系数数据
            latest_data=$(tail -n 1 postProcessing/forceCoeffs/0/coefficient.dat)
            if [[ $latest_data =~ ^[0-9] ]]; then
                time=$(echo $latest_data | awk '{print $1}')
                cd_total=$(echo $latest_data | awk '{print $2}')
                cl_total=$(echo $latest_data | awk '{print $4}')
                cd_pressure=$(echo $latest_data | awk '{print $3}')
                cd_viscous=$(echo $latest_data | awk '{printf "%.6f", $2-$3}')
                
                # 写入监控日志
                printf "%8.3f  %10.6f  %10.6f  %10.6f  %10.6f\n" \
                    $time $cd_total $cl_total $cd_pressure $cd_viscous >> $monitor_log
                
                # 实时显示
                printf "\r时间: %6.2fs | Cd: %6.4f | Cl: %6.4f" $time $cd_total $cl_total
            fi
        fi
    done
    
    echo  # 换行
    wait $solver_pid
    solver_exit_code=$?
    
    if [ $solver_exit_code -eq 0 ]; then
        print_success "求解完成"
    else
        print_error "求解失败，退出代码: $solver_exit_code"
        exit 1
    fi
}

# 生成结果报告
generate_report() {
    print_header "仿真结果报告"
    
    if [ -f "force_coefficients_monitor.log" ]; then
        # 计算平均力系数（取最后20%的数据）
        total_lines=$(wc -l < force_coefficients_monitor.log)
        skip_lines=$((total_lines - total_lines/5))
        
        if [ $skip_lines -gt 2 ]; then
            avg_cd=$(tail -n +$skip_lines force_coefficients_monitor.log | \
                    grep -v "^#" | awk '{sum+=$2; count++} END {if(count>0) print sum/count}')
            avg_cl=$(tail -n +$skip_lines force_coefficients_monitor.log | \
                    grep -v "^#" | awk '{sum+=$3; count++} END {if(count>0) print sum/count}')
            
            echo "力系数统计（最后20%数据平均值）："
            echo "  - 平均阻力系数 Cd = ${avg_cd:-N/A}"
            echo "  - 平均升力系数 Cl = ${avg_cl:-N/A}"
        fi
        
        print_success "详细力系数数据已保存至: force_coefficients_monitor.log"
    fi
    
    echo "生成的日志文件："
    ls -la log.* 2>/dev/null | awk '{print "  - " $9 " (" $5 " bytes)"}'
    
    if [ -d "postProcessing" ]; then
        echo "后处理数据目录: postProcessing/"
    fi
}

# 主程序
main() {
    print_header "3D圆柱绕流自动化仿真 (Re=100)"
    
    # 检查环境
    check_openfoam
    
    # 执行仿真流程
    generate_mesh
    check_mesh_quality
    run_simulation
    generate_report
    
    print_success "仿真流程全部完成！"
}

# 脚本入口
main "$@"
