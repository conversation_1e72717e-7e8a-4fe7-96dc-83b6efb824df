/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | cfMesh: A library for mesh generation          |
|  \\    /   O peration     | cartesianMesh - Cartesian Mesh Generator       |
|   \\  /    A nd           | Author: <PERSON><PERSON><PERSON>                         |
|    \\/     M anipulation  | E-mail: <EMAIL>            |
\*---------------------------------------------------------------------------*/

FoamFile
{
    version   2.0;
    format    ascii;
    class     dictionary;
    location  "system";
    object    meshDict;
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

// 使用合并的表面网格文件
surfaceFile "constant/triSurface/combined_patches.stl";

// 基础网格尺寸 - 控制在5万单元以内
maxCellSize 0.1;

// 局部细化设置 - 只对圆柱表面细化
localRefinement
{
    // 圆柱表面细化
    cylinder
    {
        cellSize 0.02;
    }
}

// 边界层生成 - 圆柱表面2层边界层
boundaryLayers
{
    // 在圆柱表面生成边界层
    patchBoundaryLayers
    {
        cylinder
        {
            nLayers           2;              // 2层边界层
            thicknessRatio    1.3;            // 拉伸比
            maxFirstLayerThickness 0.005;     // 第一层厚度
            allowDiscontinuity 0;             // 不允许不连续
        }
    }
}

// 边界条件定义 - 根据STL文件中的patch名称
renameBoundary
{
    defaultName defaultFaces;
    defaultType wall;

    // 定义各个边界面 - 精确匹配STL中的patch名称
    newPatchNames
    {
        "inlet"
        {
            newName inlet;
            newType patch;
        }
        "outlet"
        {
            newName outlet;
            newType patch;
        }
        "cylinder"
        {
            newName cylinder;
            newType wall;
        }
        "topWall"
        {
            newName topWall;
            newType wall;
        }
        "bottomWall"
        {
            newName bottomWall;
            newType wall;
        }
        "front"
        {
            newName front;
            newType cyclic;
            neighbourPatch back;
        }
        "back"
        {
            newName back;
            newType cyclic;
            neighbourPatch front;
        }
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
