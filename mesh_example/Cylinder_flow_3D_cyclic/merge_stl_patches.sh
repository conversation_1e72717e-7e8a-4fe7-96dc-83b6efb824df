#!/bin/bash

# 合并STL patch文件，修改solid名称以便cfMesh识别

OUTPUT_FILE="constant/triSurface/combined_patches.stl"
PATCH_DIR="constant/triSurface/patch"

# 清空输出文件
> "$OUTPUT_FILE"

# 处理每个patch文件
for stl_file in "$PATCH_DIR"/*.stl; do
    if [ -f "$stl_file" ]; then
        # 获取文件名（不含扩展名）作为patch名称
        patch_name=$(basename "$stl_file" .stl)
        
        echo "处理patch: $patch_name"
        
        # 修改solid名称并追加到输出文件
        sed "s/^solid .*/solid $patch_name/" "$stl_file" >> "$OUTPUT_FILE"
        
        # 在文件之间添加空行（可选）
        echo "" >> "$OUTPUT_FILE"
    fi
done

echo "STL文件合并完成: $OUTPUT_FILE"
echo "包含的patches:"
grep "^solid" "$OUTPUT_FILE"
