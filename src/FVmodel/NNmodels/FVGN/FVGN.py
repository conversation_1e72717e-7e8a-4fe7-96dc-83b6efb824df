import sys
import os
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)
import torch
import torch.nn as nn
from FVmodel.NNmodels.FVGN.EncoderProcesserDecoder import OptimizedGnBlock, OptimizedEncoder, OptimizedDecoder, build_mlp_from_num_layer


class Processor(nn.Module):
    def __init__(self,message_passing_num=0, hidden_size=128, drop_out=False):
        super(Processor, self).__init__()

        if message_passing_num<1:
            raise ValueError("message_passing_num must be greater than 0")
        
        GN_block_list = []
        for _ in range(message_passing_num):
            GN_block_list.append(
                OptimizedGnBlock(
                    hidden_size=hidden_size,
                    drop_out=drop_out,
                )
            )
        self.GN_block_list = nn.ModuleList(GN_block_list)


    def forward(self, latent_graph):

        # 处理剩余的模型
        for model in self.GN_block_list:
            latent_graph = model(latent_graph)

        return latent_graph


class Simulator(nn.Module):
    def __init__(
        self,
        message_passing_num,
        edge_input_size,
        node_input_size,
        node_output_size,
        drop_out=False,
        hidden_size=128,
        params=None,
    ):
        super(Simulator, self).__init__()

        self.encoder = OptimizedEncoder(
            node_input_size=node_input_size,
            edge_input_size=edge_input_size,
            hidden_size=hidden_size,
        )

        self.processer = Processor(
                    message_passing_num=message_passing_num, 
                    hidden_size=hidden_size, 
                    drop_out=False
                )

        self.decoder = OptimizedDecoder(
            hidden_size=hidden_size,
            node_output_size=node_output_size,
        )
        
    @torch.compile(mode="max-autotune")
    def forward(
        self,
        x,
        edge_attr,
        edge_index,
        batch,
    ):
        # Optimized encoding - returns tensors directly
        x, edge_attr = self.encoder(
            x,
            edge_attr
        )

        # Process through attention processors
        for model in self.processpr_list:
            x, edge_attr = model(x, edge_index, edge_attr, batch)
                
        x = x.to(torch.float32)
        edge_attr = edge_attr.to(torch.float32)
        
        ''' collected cell scheme'''
        pred_cell = self.decoder(x)
        
        return pred_cell.to(torch.float32)