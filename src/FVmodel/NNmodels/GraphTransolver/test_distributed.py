#!/usr/bin/env python3
"""
测试GraphTransolver分布式功能的脚本
使用GPU 3-7进行测试
"""
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
import os
import sys
import time
import numpy as np

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from GraphTransolver import (
        Graph_Physics_Attention_1D_Distributed,
        Transolver_distributed,
        Transolver_block_distributed,
        Graph_Physics_Attention_1D,  # 原始版本用于对比
        Transolver  # 原始版本用于对比
    )
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保GraphTransolver.py在当前目录下")
    sys.exit(1)

# 配置使用的GPU编号 (3-7号GPU)
AVAILABLE_GPUS = [3, 4, 5, 6, 7]
WORLD_SIZE = len(AVAILABLE_GPUS)

def setup(rank, world_size):
    """初始化分布式环境"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'

    # 映射rank到实际GPU编号
    actual_gpu = AVAILABLE_GPUS[rank]

    # 初始化进程组
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    torch.cuda.set_device(actual_gpu)

    if rank == 0:
        print(f"🔧 分布式环境初始化完成")
        print(f"   使用GPU: {AVAILABLE_GPUS}")
        print(f"   进程数: {world_size}")
        print(f"   当前进程rank={rank}, GPU={actual_gpu}")

def cleanup():
    """清理分布式环境"""
    if dist.is_initialized():
        dist.destroy_process_group()

def create_test_graph_data(device, total_nodes=1000, dim=256, num_graphs=4):
    """创建测试用的图数据"""
    torch.manual_seed(42 + dist.get_rank() if dist.is_initialized() else 42)

    # 创建节点特征
    x = torch.randn(total_nodes, dim, device=device)

    # 创建batch信息 - 确保每个图有合理的节点数
    base_size = total_nodes // num_graphs
    graph_sizes = []
    remaining = total_nodes

    for i in range(num_graphs - 1):
        size = base_size + torch.randint(-50, 51, (1,)).item()
        size = max(50, min(size, remaining - 50 * (num_graphs - i - 1)))
        graph_sizes.append(size)
        remaining -= size
    graph_sizes.append(remaining)

    # 创建batch索引
    batch_list = []
    for i, size in enumerate(graph_sizes):
        batch_list.extend([i] * size)
    batch = torch.tensor(batch_list, device=device).unsqueeze(1)

    return x, batch, graph_sizes

def test_distributed_attention(rank, world_size, device):
    """测试分布式注意力机制"""
    try:
        # 创建测试数据
        total_nodes = 1200
        dim = 256
        heads = 8
        slice_num = 32

        x, batch, graph_sizes = create_test_graph_data(device, total_nodes, dim)

        if rank == 0:
            print(f"\n📊 测试数据信息:")
            print(f"   总节点数: {total_nodes}")
            print(f"   特征维度: {dim}")
            print(f"   图数量: {len(graph_sizes)}")
            print(f"   各图节点数: {graph_sizes}")

        # 创建分布式注意力模型
        model = Graph_Physics_Attention_1D_Distributed(
            dim=dim,
            heads=heads,
            dim_head=dim//heads,
            dropout=0.0,
            slice_num=slice_num
        ).to(device)

        # 测试前向传播
        model.eval()
        start_time = time.time()

        with torch.no_grad():
            output = model.distributed_forward(x, batch)

        torch.cuda.synchronize()
        forward_time = time.time() - start_time

        # 验证输出形状
        expected_shape = (total_nodes, dim)
        assert output.shape == expected_shape, f"输出形状错误: {output.shape} vs {expected_shape}"

        # 验证输出不包含NaN或Inf
        assert torch.isfinite(output).all(), "输出包含NaN或Inf"

        if rank == 0:
            print(f"✅ 分布式注意力测试通过")
            print(f"   输入形状: {x.shape}")
            print(f"   输出形状: {output.shape}")
            print(f"   前向时间: {forward_time:.4f}s")
            print(f"   输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")

    except Exception as e:
        print(f"❌ Rank {rank} 分布式注意力测试失败: {e}")
        raise

def test_distributed_transolver(rank, world_size, device):
    """测试完整的分布式Transolver"""
    actual_gpu = AVAILABLE_GPUS[rank]

    try:
        # 创建测试数据
        total_nodes = 1000
        input_dim = 128

        x, batch, graph_sizes = create_test_graph_data(device, total_nodes, input_dim)

        if rank == 0:
            print(f"\n🏗️  创建分布式Transolver模型...")

        # 创建分布式Transolver模型
        model = Transolver_distributed(
            space_dim=1,
            n_layers=3,
            hidden_size=256,
            n_head=8,
            fun_dim=input_dim,
            out_dim=1,
            slice_num=16,
            dropout=0.0
        ).to(device)

        if rank == 0:
            param_count = sum(p.numel() for p in model.parameters())
            print(f"   模型参数量: {param_count:,}")
            print(f"   隐藏维度: 256")
            print(f"   注意力头数: 8")
            print(f"   层数: 3")

        # 测试前向传播
        model.eval()
        start_time = time.time()

        with torch.no_grad():
            output = model((x, batch))

        torch.cuda.synchronize()
        forward_time = time.time() - start_time

        # 验证输出
        expected_shape = (total_nodes, 1)
        assert output.shape == expected_shape, f"输出形状错误: {output.shape} vs {expected_shape}"
        assert torch.isfinite(output).all(), "输出包含NaN或Inf"

        if rank == 0:
            print(f"✅ 分布式Transolver测试通过")
            print(f"   输入形状: {x.shape}")
            print(f"   输出形状: {output.shape}")
            print(f"   前向时间: {forward_time:.4f}s")
            print(f"   输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")

    except Exception as e:
        print(f"❌ Rank {rank} 分布式Transolver测试失败: {e}")
        raise

def test_compatibility_mode(rank, world_size, device):
    """测试兼容模式（Transolver_plus风格输入）"""
    try:
        if rank == 0:
            print(f"\n🔄 测试兼容模式 (Transolver_plus风格)...")

        # 创建Transolver_plus风格的输入
        torch.manual_seed(42 + rank)
        B, N, C = 2, 500, 128

        x = torch.randn(B, N, C, device=device)
        pos = torch.randn(B, N, 3, device=device)
        condition = torch.randn(3, device=device)

        if rank == 0:
            print(f"   批次大小: {B}")
            print(f"   每批节点数: {N}")
            print(f"   特征维度: {C}")

        # 创建分布式Transolver模型
        model = Transolver_distributed(
            space_dim=3,
            n_layers=2,
            hidden_size=128,
            n_head=4,
            fun_dim=C,
            out_dim=1,
            slice_num=16,
            dropout=0.0
        ).to(device)

        # 测试前向传播
        model.eval()
        start_time = time.time()

        with torch.no_grad():
            output = model((x, pos, condition))

        torch.cuda.synchronize()
        forward_time = time.time() - start_time

        # 验证输出
        expected_shape = (B * N, 1)
        assert output.shape == expected_shape, f"输出形状错误: {output.shape} vs {expected_shape}"
        assert torch.isfinite(output).all(), "输出包含NaN或Inf"

        if rank == 0:
            print(f"✅ 兼容模式测试通过")
            print(f"   输入形状: {x.shape}")
            print(f"   位置编码: {pos.shape}")
            print(f"   条件编码: {condition.shape}")
            print(f"   输出形状: {output.shape}")
            print(f"   前向时间: {forward_time:.4f}s")
            print(f"   输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")

    except Exception as e:
        print(f"❌ Rank {rank} 兼容模式测试失败: {e}")
        raise

def test_memory_usage(rank, world_size, device):
    """测试内存使用情况"""
    actual_gpu = AVAILABLE_GPUS[rank]

    try:
        if rank == 0:
            print(f"\n💾 测试内存使用情况...")

        # 清理GPU内存
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats(actual_gpu)

        # 创建大规模测试数据
        total_nodes = 5000
        input_dim = 256

        x, batch, graph_sizes = create_test_graph_data(device, total_nodes, input_dim)

        # 记录初始内存
        initial_memory = torch.cuda.memory_allocated(actual_gpu) / 1024**2  # MB

        # 创建模型
        model = Transolver_distributed(
            space_dim=1,
            n_layers=4,
            hidden_size=512,
            n_head=16,
            fun_dim=input_dim,
            out_dim=1,
            slice_num=64,
            dropout=0.0
        ).to(device)

        model_memory = torch.cuda.memory_allocated(actual_gpu) / 1024**2  # MB

        # 前向传播
        model.eval()
        with torch.no_grad():
            output = model((x, batch))

        peak_memory = torch.cuda.max_memory_allocated(actual_gpu) / 1024**2  # MB
        final_memory = torch.cuda.memory_allocated(actual_gpu) / 1024**2  # MB

        if rank == 0:
            print(f"✅ 内存使用测试完成")
            print(f"   数据规模: {total_nodes} 节点, {input_dim} 维特征")
            print(f"   模型规模: 4层, 512隐藏维度, 16头")
            print(f"   初始内存: {initial_memory:.1f} MB")
            print(f"   模型内存: {model_memory - initial_memory:.1f} MB")
            print(f"   峰值内存: {peak_memory:.1f} MB")
            print(f"   最终内存: {final_memory:.1f} MB")

    except Exception as e:
        print(f"❌ Rank {rank} 内存测试失败: {e}")
        raise

def run_tests(rank, world_size):
    """运行所有测试"""
    # 初始化分布式环境
    setup(rank, world_size)

    actual_gpu = AVAILABLE_GPUS[rank]
    device = torch.device(f'cuda:{actual_gpu}')

    if rank == 0:
        print("=" * 80)
        print("GraphTransolver 分布式功能测试")
        print(f"使用GPU: {AVAILABLE_GPUS}")
        print("=" * 80)

    try:
        # 同步所有进程
        dist.barrier()

        # 测试1: 分布式注意力
        if rank == 0:
            print("\n1️⃣  测试分布式注意力机制...")
        test_distributed_attention(rank, world_size, device)

        # 同步
        dist.barrier()

        # 测试2: 完整分布式Transolver
        if rank == 0:
            print("\n2️⃣  测试完整分布式Transolver...")
        test_distributed_transolver(rank, world_size, device)

        # 同步
        dist.barrier()

        # 测试3: 兼容模式
        if rank == 0:
            print("\n3️⃣  测试兼容模式...")
        test_compatibility_mode(rank, world_size, device)

        # 同步
        dist.barrier()

        # 测试4: 内存使用
        if rank == 0:
            print("\n4️⃣  测试内存使用...")
        test_memory_usage(rank, world_size, device)

        if rank == 0:
            print("\n" + "=" * 80)
            print("🎉 所有分布式测试通过！")
            print("✅ GraphTransolver现在支持多GPU训练")
            print(f"✅ 测试使用GPU: {AVAILABLE_GPUS}")
            print("=" * 80)

    except Exception as e:
        if rank == 0:
            print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        # 清理分布式环境
        cleanup()

def check_environment():
    """检查测试环境"""
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行分布式测试")
        return False

    total_gpus = torch.cuda.device_count()
    print(f"🔍 检测到 {total_gpus} 个GPU")

    # 检查指定的GPU是否可用
    for gpu_id in AVAILABLE_GPUS:
        if gpu_id >= total_gpus:
            print(f"❌ GPU {gpu_id} 不存在，总共只有 {total_gpus} 个GPU")
            return False

        # 测试GPU是否可用
        try:
            torch.cuda.set_device(gpu_id)
            test_tensor = torch.randn(10, 10, device=f'cuda:{gpu_id}')
            _ = test_tensor @ test_tensor.T
            print(f"✅ GPU {gpu_id} 可用")
        except Exception as e:
            print(f"❌ GPU {gpu_id} 不可用: {e}")
            return False

    if len(AVAILABLE_GPUS) < 2:
        print(f"❌ 需要至少2个GPU进行分布式测试，当前配置: {AVAILABLE_GPUS}")
        return False

    return True

if __name__ == '__main__':
    print("=" * 80)
    print("GraphTransolver 分布式功能测试")
    print("=" * 80)

    # 检查环境
    if not check_environment():
        sys.exit(1)

    print(f"🚀 开始分布式测试")
    print(f"   使用GPU: {AVAILABLE_GPUS}")
    print(f"   进程数: {WORLD_SIZE}")
    print("=" * 80)

    try:
        # 启动多进程分布式测试
        mp.spawn(run_tests, args=(WORLD_SIZE,), nprocs=WORLD_SIZE, join=True)
        print("\n🎉 所有测试完成！")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
