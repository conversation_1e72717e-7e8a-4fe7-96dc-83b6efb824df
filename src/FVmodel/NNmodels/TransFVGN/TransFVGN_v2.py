import sys
import os
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)
import torch
import torch.nn as nn
from FVmodel.NNmodels.FVGN.EncoderProcesserDecoder import OptimizedGnBlock, OptimizedEncoder, OptimizedDecoder
from FVmodel.NNmodels.GraphTransolver.GraphTransolver import Graph_Transolver_block

class AttnProcessor(nn.Module):
    def __init__(self, message_passing_num=0, hidden_size=128, drop_out=False, dtype=torch.float32,):
        super(AttnProcessor, self).__init__()

        if message_passing_num < 1:
            raise ValueError("message_passing_num must be greater than 0")
        
        self.gn_blocks_1st = OptimizedGnBlock(
                hidden_size=hidden_size,
                drop_out=drop_out,
            )
        
        self.gn_blocks_list = nn.ModuleList([
            OptimizedGnBlock(
                hidden_size=hidden_size,
                drop_out=drop_out,
            ) for _ in range(message_passing_num-1)
        ])

        self.TransBlock = Graph_Transolver_block(
            num_heads=8,
            hidden_dim=hidden_size,
            dropout=0,
            act="gelu",
            mlp_ratio=2,
            slice_num=32
        )

    def forward(self, x, edge_index, edge_attr, batch):

        # Process through GN blocks (optimized - no Data object creation)
        fx, f_edge_attr = self.gn_blocks_1st(x, edge_index, edge_attr)
        for  gn_block in self.gn_blocks_list:
                fx, f_edge_attr = gn_block(fx, edge_index, f_edge_attr)

        # Apply transformer block with residual connection
        fx = self.TransBlock((x + fx), batch)
        
        return fx, f_edge_attr


class Simulator(nn.Module):
    def __init__(
        self,
        message_passing_num,
        edge_input_size,
        node_input_size,
        node_output_size,
        drop_out=False,
        hidden_size=128,
        params=None,
    ):
        super(Simulator, self).__init__()

        self.encoder = OptimizedEncoder(
            node_input_size=node_input_size,
            edge_input_size=edge_input_size,
            hidden_size=hidden_size,
        )

        processpr_list = []
        for _ in range(params.attn_processor_num):
            processpr_list.append(
                AttnProcessor(
                    message_passing_num=message_passing_num, 
                    hidden_size=hidden_size, 
                    drop_out=drop_out
                )
            )
        self.processpr_list = nn.ModuleList(processpr_list)

        self.decoder = OptimizedDecoder(
            hidden_size=hidden_size,
            node_output_size=node_output_size,
        )
        
        
    @torch.compile(mode="max-autotune")
    def forward(
        self,
        x,
        edge_attr,
        edge_index,
        batch,
    ):

        # Optimized encoding - returns tensors directly
        x, edge_attr = self.encoder(
            x,
            edge_attr
        )

        # Process through attention processors
        for model in self.processpr_list:
            x, edge_attr = model(x, edge_index, edge_attr, batch)
        ''' collected cell scheme'''
        pred_cell = self.decoder(x)
        
        return pred_cell.to(torch.float32)