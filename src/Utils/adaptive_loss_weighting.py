#!/usr/bin/env python3
"""
Adaptive Loss Weighting for Physics-Informed Neural Networks
Based on gradient norm balancing method from multi-task learning

Reference:
- "Self-adaptive loss balanced Physics-informed neural networks"
- "Gradnorm: Gradient normalization for adaptive loss balancing"
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple


class PINNAdaptiveLossWeighting:
    """
    PINN-specific adaptive loss weighting based on the self-adaptive learning rate annealing scheme.

    This implementation follows the exact formulation from the paper:
    L(θ) = λ_ic * L_ic(θ) + λ_bc * L_bc(θ) + λ_f * L_f(θ)

    Where global weights are computed as:
    λ_□ = (||∇_θ L_ic(θ)|| + ||∇_θ L_bc(θ)|| + ||∇_θ L_pde(θ)||) / ||∇_θ L_□(θ)||

    This formulation equalizes the gradient norms of weighted losses, preventing bias toward
    any particular term during training.
    """

    def __init__(
        self,
        update_frequency: int = 100,
        eps: float = 1e-8,
        max_weight: float = 1000.0,
        min_weight: float = 1e-6
    ):
        """
        Initialize PINN adaptive loss weighting.

        Args:
            update_frequency: How often to update weights (typically every 100-1000 iterations)
            eps: Small constant for numerical stability
            max_weight: Maximum allowed weight value
            min_weight: Minimum allowed weight value
        """
        self.update_frequency = update_frequency
        self.eps = eps
        self.max_weight = max_weight
        self.min_weight = min_weight
        self.step_count = 0

        # Initialize weights to 1.0
        self.lambda_cont = 1e5
        self.lambda_momentum = 1e4

        # Track gradient norms for monitoring
        self.grad_norm_history = {'cont': [], 'momentum': []}
        self.weight_history = {'cont': [], 'momentum': []}

    def compute_gradient_norms(
        self,
        loss_cont: torch.Tensor,
        loss_momentum: torch.Tensor,
        model: nn.Module
    ) -> Tuple[float, float]:
        """
        Compute gradient norms for continuity and momentum losses.

        Args:
            loss_cont: Continuity loss tensor [N_graph, 1]
            loss_momentum: Momentum loss tensor [N_graph, 3]
            model: Neural network model

        Returns:
            Tuple of (grad_norm_cont, grad_norm_momentum)
        """
        # Get model parameters for gradient computation
        params = list(model.parameters())
        target_params = [p for p in params if p.requires_grad]

        grad_norm_cont = 0.0
        grad_norm_momentum = 0.0

        try:
            # Method 1: Try with separate gradient computations (torch.compile compatible)
            # Compute total losses for gradient computation
            total_loss_cont = torch.sum(loss_cont)
            total_loss_momentum = torch.sum(loss_momentum)

            # Compute gradients for continuity loss
            grads_cont = torch.autograd.grad(
                outputs=total_loss_cont,
                inputs=target_params,
                retain_graph=True,
                create_graph=False,
                allow_unused=True
            )

            # Compute L2 norm of continuity gradients
            for grad in grads_cont:
                if grad is not None:
                    grad_norm_cont += torch.norm(grad, p=2).item() ** 2
            grad_norm_cont = np.sqrt(grad_norm_cont)

            # Compute gradients for momentum loss (separate computation)
            grads_momentum = torch.autograd.grad(
                outputs=total_loss_momentum,
                inputs=target_params,
                retain_graph=True,
                create_graph=False,
                allow_unused=True
            )

            # Compute L2 norm of momentum gradients
            for grad in grads_momentum:
                if grad is not None:
                    grad_norm_momentum += torch.norm(grad, p=2).item() ** 2
            grad_norm_momentum = np.sqrt(grad_norm_momentum)

        except RuntimeError:
            try:
                # Method 2: Fallback to loss magnitude ratio method
                print(f"Warning: Direct gradient computation failed, using loss magnitude ratio method")

                # Use relative loss magnitudes as proxy for gradient norms
                loss_cont_mag = float(torch.sum(torch.abs(loss_cont)).item())
                loss_momentum_mag = float(torch.sum(torch.abs(loss_momentum)).item())

                # Scale by typical gradient-to-loss ratios (empirical values)
                grad_norm_cont = loss_cont_mag * 10.0  # Typical scaling factor
                grad_norm_momentum = loss_momentum_mag * 10.0

            except Exception:
                # Method 3: Final fallback - use equal weights
                print(f"Warning: All gradient computation methods failed, using equal weights")
                grad_norm_cont = 1.0
                grad_norm_momentum = 1.0

        return grad_norm_cont, grad_norm_momentum

    def update_weights(
        self,
        loss_cont: torch.Tensor,
        loss_momentum: torch.Tensor,
        model: nn.Module
    ) -> Tuple[float, float]:
        """
        Update loss weights based on gradient norm balancing.

        Args:
            loss_cont: Continuity loss tensor [N_graph, 1]
            loss_momentum: Momentum loss tensor [N_graph, 3]
            model: Neural network model

        Returns:
            Tuple of (lambda_cont, lambda_momentum)
        """
        self.step_count += 1

        # Update weights only at specified frequency
        if self.step_count % self.update_frequency == 0:
            # Compute gradient norms
            grad_norm_cont, grad_norm_momentum = self.compute_gradient_norms(
                loss_cont, loss_momentum, model
            )

            # Store gradient norms for monitoring
            self.grad_norm_history['cont'].append(grad_norm_cont)
            self.grad_norm_history['momentum'].append(grad_norm_momentum)

            # Compute adaptive weights according to the paper's formula:
            # λ_□ = (||∇_θ L_cont|| + ||∇_θ L_momentum||) / ||∇_θ L_□||
            total_grad_norm = grad_norm_cont + grad_norm_momentum

            if grad_norm_cont > self.eps:
                self.lambda_cont = total_grad_norm / (grad_norm_cont + self.eps)
            else:
                self.lambda_cont = 1.0

            if grad_norm_momentum > self.eps:
                self.lambda_momentum = total_grad_norm / (grad_norm_momentum + self.eps)
            else:
                self.lambda_momentum = 1.0

            # Clip weights to prevent extreme values
            self.lambda_cont = np.clip(self.lambda_cont, self.min_weight, self.max_weight)
            self.lambda_momentum = np.clip(self.lambda_momentum, self.min_weight, self.max_weight)

        # Store weight history
        self.weight_history['cont'].append(self.lambda_cont)
        self.weight_history['momentum'].append(self.lambda_momentum)

        return self.lambda_cont, self.lambda_momentum

    def get_weighted_loss(
        self,
        loss_cont: torch.Tensor,
        loss_momentum: torch.Tensor,
        model: nn.Module
    ) -> Tuple[torch.Tensor, float, float]:
        """
        Compute weighted total loss according to PINN adaptive weighting.

        Args:
            loss_cont: Continuity loss tensor [N_graph, 1]
            loss_momentum: Momentum loss tensor [N_graph, 3]
            model: Neural network model

        Returns:
            Tuple of (total_weighted_loss, lambda_cont, lambda_momentum)
        """
        # Update weights
        lambda_cont, lambda_momentum = self.update_weights(loss_cont, loss_momentum, model)

        # Compute weighted losses
        weighted_loss_cont = lambda_cont * torch.sum(loss_cont)
        weighted_loss_momentum = lambda_momentum * torch.sum(loss_momentum)

        # Total weighted loss
        total_loss = weighted_loss_cont + weighted_loss_momentum

        return total_loss, lambda_cont, lambda_momentum

    def get_current_weights(self) -> Tuple[float, float]:
        """Get current loss weights."""
        return self.lambda_cont, self.lambda_momentum

    def reset_weights(self):
        """Reset weights to initial values."""
        self.lambda_cont = 1.0
        self.lambda_momentum = 1.0
        self.step_count = 0

    def print_statistics(self):
        """Print current statistics."""
        print(f"\n📊 PINN Adaptive Loss Weighting Statistics (Step {self.step_count}):")
        print("-" * 60)
        print(f"{'Loss Term':<15} {'Weight':<10} {'Avg Grad Norm':<15}")
        print("-" * 60)

        avg_grad_cont = np.mean(self.grad_norm_history['cont']) if self.grad_norm_history['cont'] else 0.0
        avg_grad_momentum = np.mean(self.grad_norm_history['momentum']) if self.grad_norm_history['momentum'] else 0.0

        print(f"{'Continuity':<15} {self.lambda_cont:<10.4f} {avg_grad_cont:<15.6f}")
        print(f"{'Momentum':<15} {self.lambda_momentum:<10.4f} {avg_grad_momentum:<15.6f}")
        print("-" * 60)