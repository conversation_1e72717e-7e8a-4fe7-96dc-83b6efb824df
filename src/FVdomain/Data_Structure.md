# FV Domain HeteroData 数据结构文档

## 概述

本文档详细描述了基于PyTorch Geometric HeteroData的有限体积(FV)域图数据结构。该结构将原始的多个独立图数据集（GraphNodeDataset, GraphFaceDataset, GraphCellDataset等）统一为一个异构图对象。

## 文件结构

```
src/FVdomain/Graph_hetero_dataset.py  # HeteroGraphBase和HeteroGraphDataset定义
src/FVdomain/Graph_hetero_loader.py   # 数据加载器和兼容性包装器
```

## HeteroData 数据结构

### 节点类型 (Node Types)

#### 1. `'node'` - 物理网格节点
存储网格的物理节点/顶点信息。

**属性:**
- `pos`: `[num_nodes, 3]` - 节点的3D坐标位置 (x, y, z)
- `num_nodes`: `int` - 节点总数

#### 2. `'face'` - 网格面
存储网格面的几何和类型信息。

**属性:**
- `pos`: `[num_faces, 3]` - 面中心的3D坐标位置
- `face_area`: `[num_faces, 1]` - 每个面的面积
- `face_type`: `[num_faces]` - 面的类型标识（边界类型等）
- `num_nodes`: `int` - 面的总数

#### 3. `'cell'` - 网格单元
存储网格单元的几何信息。

**属性:**
- `cells_volume`: `[num_cells, 1]` - 每个单元的体积
- `cells_face_unv`: `[num_cells, max_faces_per_cell, 3]` - 单元面的单位法向量
- `num_nodes`: `int` - 单元总数

#### 4. `'cpd_cell'` - 复合单元 (用于WLSQ)
存储用于加权最小二乘(WLSQ)计算的复合单元信息。

**属性:**
- `pos`: `[num_cpd_cells, 3]` - 复合单元的质心坐标
- `x`: `[num_cpd_cells, 4+]` - 特征向量，包含uvwp（速度和压力）+ theta_PDE参数
- `y`: `[num_cpd_cells, 4]` - 目标值，包含目标uvwp
- `cell_type`: `[num_cpd_cells, 1]` - 单元类型标识
- `global_idx`: `[num_cpd_cells]` - 全局索引，用于数据池更新
- `A_cell_to_cell`: `[num_cpd_cells, num_cpd_cells]` - WLSQ系数矩阵A
- `single_B_cell_to_cell`: `[num_cpd_cells, num_cpd_cells]` - WLSQ系数矩阵B
- `num_nodes`: `int` - 复合单元总数

#### 5. `'graph'` - 图级别属性
存储整个图的全局参数和元数据。

**属性:**
- `theta_PDE`: `[5]` - PDE参数向量
- `uvwp_dim`: `[1]` - uvwp维度信息
- `dt_graph`: `[1]` - 时间步长
- `case_name`: `[case_name_length]` - 案例名称（字符编码）
- `graph_index`: `[1]` - 图索引
- `num_nodes`: `int` - 固定为1

### 边类型 (Edge Types)

#### 1. `('face', 'connects_to', 'node')` - 面-节点连接
描述面与其组成节点之间的连接关系。

**属性:**
- `face_node`: `[2, num_face_node_edges]` - 面-节点连接的边索引

#### 2. `('cell', 'contains', 'node')` - 单元-节点连接
描述单元包含哪些节点。

**属性:**
- `cells_node`: `[total_cell_nodes]` - 所有单元的节点索引（展平）
- `cells_node_ptr`: `[num_cells]` - 每个单元在cells_node中的起始位置

#### 3. `('cell', 'bounded_by', 'face')` - 单元-面连接
描述单元由哪些面围成。

**属性:**
- `cells_face`: `[total_cell_faces]` - 所有单元的面索引（展平）
- `cells_face_ptr`: `[num_cells]` - 每个单元在cells_face中的起始位置

#### 4. `('face', 'bounds', 'cell')` - 面-单元连接
描述面围成哪些单元的反向关系。

**属性:**
- `face_node_ptr`: `[num_faces]` - 每个面的节点指针

#### 5. `('cpd_cell', 'neighbors', 'cpd_cell')` - 单元邻居关系
描述复合单元之间的邻接关系。

**属性:**
- `cpd_neighbor_cell`: `[2, num_neighbor_edges]` - 邻居单元的边索引

#### 6. `('cpd_cell', 'wlsq_neighbors', 'cpd_cell')` - WLSQ扩展连接
描述用于WLSQ计算的扩展邻居关系。

**属性:**
- `cpd_neighbor_cell_x`: `[2, num_wlsq_edges]` - WLSQ邻居的边索引

### 全局属性

**HeteroGraphBase级别的属性:**
- `_num_nodes`: `int` - 物理节点总数，用于batching时的offset计算
- `_num_faces`: `int` - 面总数，用于batching时的offset计算
- `_num_cells`: `int` - 单元总数，用于batching时的offset计算
- `_num_cpd_cells`: `int` - 复合单元总数，用于batching时的offset计算

## Batching 行为

### Offset 规则 (`__inc__` 方法)

不同属性在batching时使用不同的offset规则：

```python
offset_rules = {
    # 连接性属性 - 需要offset
    "face_node": _num_nodes,           # 面-节点连接用节点数offset
    "cells_node": _num_nodes,          # 单元-节点连接用节点数offset
    "cells_face": _num_faces,          # 单元-面连接用面数offset
    "cpd_neighbor_cell": _num_cpd_cells,  # 单元邻居用复合单元数offset
    "cpd_neighbor_cell_x": _num_cpd_cells,    # WLSQ邻居用复合单元数offset

    # 指针属性 - 需要offset
    "cells_node_ptr": _num_cells,      # 单元指针用单元数offset
    "cells_face_ptr": _num_cells,      # 单元面指针用单元数offset
    "face_node_ptr": _num_faces,       # 面节点指针用面数offset

    # 数据属性 - 不需要offset
    "pos": 0,                          # 位置坐标
    "x": 0,                           # 特征向量
    "y": 0,                           # 目标值
    "face_area": 0,                   # 面积
    "cells_volume": 0,                # 体积
    "global_idx": 0,                  # 全局索引
    "A_cell_to_cell": 0,              # WLSQ矩阵
    "single_B_cell_to_cell": 0,       # WLSQ矩阵
    "theta_PDE": 0,                   # PDE参数
    # ... 其他数据属性
}
```

### Concatenation 维度 (`__cat_dim__` 方法)

不同属性在batching时沿不同维度concatenate：

```python
cat_dim_rules = {
    # 沿第0维concatenate（增加样本数）
    "pos": 0,                         # [N, 3] -> [N_total, 3]
    "x": 0,                          # [N, F] -> [N_total, F]
    "y": 0,                          # [N, F] -> [N_total, F]
    "face_area": 0,                  # [N, 1] -> [N_total, 1]
    "cells_volume": 0,               # [N, 1] -> [N_total, 1]
    "cells_node": 0,                 # [M] -> [M_total]
    "cells_face": 0,                 # [M] -> [M_total]
    "cpd_neighbor_cell": 0,          # [M] -> [M_total] (展平的边)
    "cpd_neighbor_cell_x": 0,            # [M] -> [M_total] (展平的边)

    # 沿第1维concatenate（增加边数）
    "face_node": 1,                  # [2, E] -> [2, E_total]

    # 其他属性使用默认规则
}
```

## 兼容性接口

为了保持与原始代码的兼容性，提供了`HeteroFVGraph`包装器，它将HeteroData映射到原始的接口：

```python
fv_graph.graph_node     # -> 访问 hetero_data['node']
fv_graph.graph_face     # -> 访问 hetero_data['face']
fv_graph.graph_cell     # -> 访问 hetero_data['cpd_cell']
fv_graph.graph_cell_x   # -> 访问 WLSQ相关属性
fv_graph.graph_index    # -> 访问 hetero_data['graph']
```

## 数据流

1. **数据加载**: `HeteroGraphDataset.get()` 从原始数据创建`HeteroGraphBase`对象
2. **Batching**: PyG的`HeteroBatch.from_data_list()` 使用自定义的`__inc__`和`__cat_dim__`规则
3. **兼容性包装**: `HeteroFVGraph` 提供原始接口访问
4. **模型计算**: 模型使用兼容性接口进行前向传播
5. **数据更新**: 通过`global_idx`更新数据池
