import torch
import numpy as np
from Utils.utilities import NodeType
from torch_scatter import scatter

def velocity_profile(
    mesh,
    mean_u,
    mask_cpd_inlet,
    mask_cpd_wall,
):
    """
    
    """
    
    cpd_centroid = mesh["cpd|centroid"]
    
    velocity_field = torch.zeros((cpd_centroid.shape[0], 3), device=cpd_centroid.device)
    pressure_field = torch.zeros((cpd_centroid.shape[0], 1), device=cpd_centroid.device)
    
    if mesh["BC_json"]["inlet_type"] == "fixedValue":
        
        velocity_field[:,0] = float(mean_u)
        
        return velocity_field.to(torch.float32), pressure_field.to(torch.float32), mean_u
    
    elif mesh["BC_json"]["inlet_type"] == "flowRateInletVelocity":
        
        rho = mesh["rho"]
        face_area = mesh["face|face_area"].view(-1,1)
        face_type = mesh["face|face_type"].long().squeeze()
        cells_face = mesh["cells_face"].long().squeeze()
        face_normal = torch.zeros((face_area.shape[0],3), device=face_area.device)
        
        mask_inlet_face = (face_type == NodeType.INFLOW)
        mask_inlet_cells_face = (face_type[cells_face] == NodeType.INFLOW)
        
        cells_face_normal= mesh["cells_face_normal"] 
        face_normal[cells_face[mask_inlet_cells_face]] = cells_face_normal[mask_inlet_cells_face]
        
        face_area_inlet = face_area[mask_inlet_face]
        face_normal_inlet = face_normal[mask_inlet_face]

        flow_rate = mean_u # TODO: Temperoarily use mean_u as flow_rate
        
        U_avg = flow_rate / torch.sum(rho*face_area_inlet)
        
        uvw_inlet = -U_avg * face_normal_inlet
        
        velocity_field[:, :] = uvw_inlet.mean(dim=0,keepdim=True).repeat(cpd_centroid.shape[0],1)
        velocity_field[mask_cpd_wall,0:3] = 0.
        velocity_field[mask_cpd_inlet] = uvw_inlet
        
        return velocity_field.to(torch.float32), pressure_field.to(torch.float32), uvw_inlet.norm(dim=1).mean()
