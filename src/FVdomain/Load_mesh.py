"""Utility functions for reading the datasets."""

import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

import matplotlib
matplotlib.use("agg")

import json
import random
import torch
from torch.utils.data import Dataset
import numpy as np
import h5py
import math
from Utils import get_param
from Utils.utilities import NodeType
from FVdomain.Set_BC import velocity_profile
from FVmodel.FVdiscretization.FVgrad import compute_normal_matrix

class CFDdatasetBase:
    # Base class for CFDdataset with process_trajectory method
    @staticmethod
    def select_PDE_coef(mesh, theta_PDE_list=None):
        (
            mean_U,
            rho,
            mu,
            source,
            aoa,
            dt,
            L,
        ) = random.choice(theta_PDE_list)
        
        mesh["rho"] = torch.tensor(rho)
        mesh["mu"] = torch.tensor(mu)
        mesh["source"] = torch.tensor(source)
        mesh["aoa"] = torch.tensor(aoa)
        mesh["dt"] = torch.tensor(dt)
        mesh["L"] = torch.tensor(L)
        
        return (
            mean_U, 
            rho, 
            mu, 
            source,
            aoa, 
            dt, 
            L,
        )

    @staticmethod
    def init_env(
        mesh,
        mean_u=None,
    ):

        # generate BC mask
        cell_type = mesh["cpd|cell_type"].long().squeeze()
        mask_cpd_inlet = (cell_type== NodeType.INFLOW)
        mask_cpd_wall = (cell_type== NodeType.WALL_BOUNDARY)
        dirichlet_mask = mask_cpd_inlet | mask_cpd_wall
        
        # init uvwp field
        uvw_cell, p_cell, uvw_mag = velocity_profile(
            mesh,
            mean_u,
            mask_cpd_inlet,
            mask_cpd_wall,
        )
        
        # set uniform initial field value
        uvwp_cell = torch.cat(
            (
                uvw_cell, 
                p_cell
            ),
            dim=1
        ).to(torch.float32)
        
        uvwp_cell[mask_cpd_wall,0:3] = 0.
        
        # store target node for dirchlet BC and make dimless if possible
        mesh["target|uvwp"] = uvwp_cell[dirichlet_mask,0:3].clone() / uvw_mag
        
        return mesh, uvwp_cell, uvw_mag

    @staticmethod
    def set_theta_PDE( mesh, uvw_mag, rho, mu, source, aoa, dt, L):
        """

        """
        U_in = uvw_mag*torch.tensor(
            [math.cos(math.radians(aoa)), math.sin(math.radians(aoa))]
        )
        
        mesh_pos = mesh["node|pos"][0]
        
        theta_PDE = mesh["theta_PDE_bak"]
        
        unsteady_coeff = theta_PDE["unsteady"]

        continuity_eq_coeff = theta_PDE["continuity"]

        convection_coeff = theta_PDE["convection"]

        grad_p_coeff = theta_PDE["grad_p"] / rho

        diffusion_coeff = (
            (mu / uvw_mag) if 0 == convection_coeff else # convection_coeff=0 means poisson equation
            (mu / (rho * uvw_mag)) # Navier-Stokes equation
        )

        source_term = source / (uvw_mag*rho) # if params.dimless else source

        dt_cell = dt * uvw_mag # if params.dimless else dt
        
        theta_PDE = torch.tensor(
            [
                unsteady_coeff, # 1
                continuity_eq_coeff, # 2
                convection_coeff, # 3
                grad_p_coeff, # 4
                diffusion_coeff, # 5
                source_term, # 6
                U_in[0].item(), # 7
                U_in[1].item(), # 8
                mesh["Re"], # 9
            ],
            device=mesh_pos.device,
            dtype=torch.float32,
        ).view(1,-1)
        mesh["theta_PDE"] = theta_PDE
        
        mesh["dt_graph"] = torch.tensor(
            [dt_cell],
            device=mesh_pos.device,
            dtype=torch.float32,
        ).view(1,-1)

        mesh["uvwp_dim"] = torch.tensor(
            [[uvw_mag, uvw_mag, uvw_mag, (uvw_mag**2)]],
            device=mesh_pos.device,
            dtype=torch.float32,
        ).view(1,-1)

        return mesh

    @staticmethod
    def calc_WLSQ_A_B_normal_matrix(mesh, order):

        if not "A_cell_to_cell" in mesh.keys():
            
            """>>> compute WLSQ cell to cell left A matrix >>>"""
            cpd_centroid = mesh["cpd|centroid"]
            cpd_neighbor_cell_x = mesh["cpd|neighbor_cell_x"].long()

            """ >>> compute WLSQ cell to cell left A matrix >>> """
            (A_cell_to_cell, two_way_B_cell_to_cell) = compute_normal_matrix(
                order=order,
                mesh_pos=cpd_centroid,
                edge_index=cpd_neighbor_cell_x, # 默认应该是仅包含1阶邻居点+构成共点的单元的所有点
            )
            
            mesh["A_cell_to_cell"] = A_cell_to_cell.to(torch.float32)
            mesh["single_B_cell_to_cell"] = (
                torch.chunk(two_way_B_cell_to_cell, 2, dim=0)[0]
            ).to(torch.float32)
            """ <<< compute WLSQ cell to cell right B matrix<<< """

        return mesh
    
    @staticmethod
    def transform_mesh(
        mesh, 
        params=None
    ):
        
        theta_PDE_list = mesh["theta_PDE_list"]
        (
            mean_u,
            rho,
            mu,
            source,
            aoa,
            dt,
            L,
        ) = CFDdatasetBase.select_PDE_coef(mesh, theta_PDE_list)

        mesh, init_uvwp_cell, uvw_mag = CFDdatasetBase.init_env(
            mesh,        
            mean_u=mean_u,
        )
        
        mesh["mean_u"] = torch.tensor(uvw_mag.item(), dtype=torch.float32)
        mesh["Re"] = torch.tensor(rho * uvw_mag.item() * L, dtype=torch.float32) / \
            mu if mu!=0 else torch.tensor(0, dtype=torch.float32)
        
        mesh = CFDdatasetBase.set_theta_PDE(
            mesh, uvw_mag, rho, mu, source, aoa, dt, L
        )

        mesh = CFDdatasetBase.calc_WLSQ_A_B_normal_matrix(mesh,params.order)
        
        return mesh, init_uvwp_cell

class H5CFDdataset(Dataset):
    def __init__(self, params, file_list):
        super().__init__()

        self.file_list = file_list
        self.params = params
        
    def __getitem__(self, index):
        path = self.file_list[index]
        file_dir = os.path.dirname(path)
        h5_file = h5py.File(path, "r")

        try:
            BC_json = json.load(open(f"{file_dir}/BC.json", "r"))
        except:
            raise ValueError(f"BC.json is not found in the {path}")

        mesh = {"case_name":h5_file.attrs['case_name']}
        
        # convert to tensors
        for key in h5_file.keys():
            value = h5_file[key][()]
            if isinstance(value, np.ndarray):
                # convert numpy array to torch tensor
                mesh[key] = torch.from_numpy(value)
            else:
                mesh[key] = value

        # # import all BC.json item into mesh dict
        # for key, value in BC_file.items():
        #     mesh["BC_json"] = BC_file
        mesh["BC_json"] = BC_json
        mesh["theta_PDE_bak"] = mesh["BC_json"]["theta_PDE"] # 后续生成单独case参数时候theta_PDE会被覆盖，所以备份一下
        
        # generate all valid theta_PDE combinations
        theta_PDE = mesh["theta_PDE_bak"]
        theta_PDE_list = (
                    get_param.generate_combinations(
                        U_range=theta_PDE["inlet"],
                        rho_range=theta_PDE["rho"],
                        mu_range=theta_PDE["mu"],
                        source_range=theta_PDE["source"],
                        aoa_range=theta_PDE["aoa"],
                        dt=theta_PDE["dt"],
                        L=theta_PDE["L"],
                        Re_max=theta_PDE["Re_max"],
                        Re_min=theta_PDE["Re_min"],
                    )
                )
        mesh["theta_PDE_list"] = theta_PDE_list
        
        # start to calculate other attributes like stencil, WLSQ matrix, etc.
        mesh_transformed, init_uvwp_cell = CFDdatasetBase.transform_mesh(
            mesh, 
            self.params
        )

        # return to CPU!
        return mesh_transformed, init_uvwp_cell 

    def __len__(self):
        return len(self.file_list)

