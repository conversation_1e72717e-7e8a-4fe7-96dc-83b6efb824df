"""
HeteroData-based loader for FV domain.
Defines loaders and compatibility wrapper for existing FVGraph interface.
"""

import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)
from datetime import datetime

import torch
from torch_geometric.data import Batch as HeteroBatch

from .Graph_hetero_dataset import HeteroGraphDataset
from Post_process.to_vtk import export_full_mesh_vtu


class HeteroDataPool:
    """
    Data pool for heterogeneous graphs.
    """
    
    def __init__(self, params=None, device=None, state_save_dir=None):
        self.params = params
        self.device = device
        
        try:
            if not (state_save_dir.find("traing_results") != -1):
                os.makedirs(f"{state_save_dir}/traing_results", exist_ok=True)
                self.state_save_dir = f"{state_save_dir}/traing_results"
        except:
            print("Warning: no state_save_dir specified")
        
        self._plot_env = True
        self.reset_env_flag = False  # 初始化reset_env_flag
        self.rst_time = 1  # 初始化rst_time
        
    def load_mesh_to_cpu(self, dataset_dir=None):
        """Loads the mesh dataset to CPU."""
        from FVdomain.Load_mesh import H5CFDdataset
        from torch.utils.data import DataLoader as torch_DataLoader
        
        valid_h5file_paths = []
        for subdir, _, files in os.walk(dataset_dir):
            for data_name in files:
                if data_name.endswith(".h5"):
                    valid_h5file_paths.append(os.path.join(subdir, data_name))
                    
        if not valid_h5file_paths:
            raise ValueError(".h5 file not found in dataset directory")
        
        print("Loading whole dataset to cpu")
        
        mesh_dataset = H5CFDdataset(params=self.params, file_list=valid_h5file_paths)
        self.meta_pool = []
        self.uvwp_cell_pool = []
        start_idx = 0
        
        while True:
            for i_data in range(len(valid_h5file_paths)):
                meta_data, init_uvwp_cell = mesh_dataset[i_data]
                meta_data["global_idx"] = torch.arange(start_idx, start_idx + init_uvwp_cell.shape[0])
                self.meta_pool.append(meta_data)
                self.uvwp_cell_pool.append(init_uvwp_cell)
                start_idx += init_uvwp_cell.shape[0]

                if len(self.meta_pool) >= self.params.dataset_size:
                    break
                print(f" Number of loaded meshes: {len(self.meta_pool)}")

            if len(self.meta_pool) >= self.params.dataset_size:
                break
            
        print("Successfully loaded whole dataset to cpu")
        
        self.uvwp_cell_pool = torch.cat(self.uvwp_cell_pool, dim=0)

        # --- DEBUGGING START ---
        print("--- Running Debug Check on meta_pool ---")
        if self.meta_pool:
            # Take the first sample as reference
            ref_sample = self.meta_pool[0]
            ref_shapes = {key: value.shape for key, value in ref_sample.items() if torch.is_tensor(value)}

            for i, sample in enumerate(self.meta_pool[1:]):
                for key, value in sample.items():
                    if torch.is_tensor(value):
                        ref_shape = ref_shapes.get(key)
                        if ref_shape is None:
                            print(f"DEBUG WARNING: Key '{key}' in sample {i+1} not in reference sample 0.")
                            continue
                        
                        # Check number of dimensions
                        if value.ndim != len(ref_shape):
                            print(f"--- DEBUG ALERT: Dimension Mismatch Found! ---")
                            print(f"  Sample Index: {i+1}")
                            print(f"  Key: '{key}'")
                            print(f"  Reference Shape (from sample 0): {ref_shape} (ndim={len(ref_shape)})")
                            print(f"  Current Shape: {value.shape} (ndim={value.ndim})")
                            print(f"  Case name: {sample.get('case_name')}, File name: {sample.get('file_name')}")
                            print(f"---------------------------------------------")

        print("--- Debug Check Finished ---")
        # --- DEBUGGING END ---
        
        # Control the folder grouping for the number of plots
        self.plot_count = 0
        return self.params
    
    @staticmethod
    def datapreprocessing_hetero(fv_graph):
        """Preprocesses the HeteroFVGraph object (compatibility wrapper)."""
        # Access through the compatibility interface
        theta_PDE_cell = fv_graph.graph_index.theta_PDE[fv_graph.graph_cell.batch]
        fv_graph.graph_cell.x = torch.cat((fv_graph.graph_cell.x, theta_PDE_cell), dim=1)
        return fv_graph
    
    def _set_reset_env_flag(self, flag=False, rst_time=1):
        """
        Sets the flag for resetting the environment.

        Args:
            flag (bool): The flag to indicate whether to reset the environment.
            rst_time (int): The number of times to reset.
        """
        self.reset_env_flag = flag
        self.rst_time = rst_time

    def refresh_pool(self, uvwp_new, global_idx, graph_index=None):
        """Updates the uvwp_cell_pool with new uvwp values."""
        self.uvwp_cell_pool[global_idx] = uvwp_new.data

        if self.reset_env_flag:
            for _ in range(getattr(self, 'rst_time', 1)):
                self.reset_env(plot=self._plot_env)
            self.reset_env_flag = False
            self._plot_env = True

    def reset_env(self, plot=False):
        """
        Resets the environment by removing the oldest mesh and adding a new one.
        This is a simplified version - you may need to implement the full logic
        from the original Data_Pool.reset_env method if needed.
        """
        # Import here to avoid circular imports
        from FVdomain.Load_mesh import CFDdatasetBase
        from Utils.utilities import NodeType

        # Pop the mesh data of the 0-th grid
        old_mesh = self.meta_pool.pop(0)
        old_global_idx = old_mesh["global_idx"]
        
        # Plotting
        if plot:
            uvwp_cell = self.uvwp_cell_pool[old_global_idx] # [num_nodes_in_old_mesh, C]
            mask_interior_cell = old_mesh["cpd|cell_type"].long()==NodeType.NORMAL

            ''' >>> plot at cell-center >>> '''
            self.export_to_tecplot(old_mesh, uvwp_cell[mask_interior_cell])
            ''' <<< plot at cell-center <<< '''

            self._plot_env = False
            
        # Remove uvwp data belonging to the 0-th grid
        self.uvwp_cell_pool = self.uvwp_cell_pool[old_global_idx.shape[0]:]

        for iidx in range(len(self.meta_pool)):
            cur_meta_data = self.meta_pool[iidx]
            cur_meta_data["global_idx"] -= old_global_idx.shape[0]

        # Generate new mesh data
        new_mesh, init_uvwp = CFDdatasetBase.transform_mesh(
            old_mesh,
            self.params
        )
        new_mesh["global_idx"] = torch.arange(
            self.uvwp_cell_pool.shape[0], self.uvwp_cell_pool.shape[0]+init_uvwp.shape[0]
        )
        self.uvwp_cell_pool = torch.cat((self.uvwp_cell_pool, init_uvwp), dim=0)
        self.meta_pool.append(new_mesh)

    def export_to_tecplot(self, mesh, uvwp, file_name=None):
        """
        Exports data to Tecplot format, supports dynamic variable identification.

        Args:
            mesh (dict): Mesh data.
                         Contains 'node|pos': [N, D_pos], 'case_name', 'cells_node': [num_cells, max_nodes_per_cell],
                         'cells_face': [num_cells, max_faces_per_cell], 'cells_index': [num_cells], 'dt', 'source', 'aoa',
                         'Re' (optional), 'face_node': [num_faces, max_nodes_per_face],
                         'face|neighbor_cell': [num_faces, 2], 'rho', 'mu'.
            uvwp (torch.Tensor): Main physical variable data, typically U, V, P. Shape: [num_elements, C] where num_elements
                                depends on datalocation (N for nodes, num_cells for cells).
            datalocation (str): Data location ("node" or "cell").
            file_name (str, optional): Output file name. Defaults to None.
        """
        
        # write vtk for visualization
        mesh_pos = mesh["node|pos"] # [N, D_pos]
        case_name = mesh["case_name"]
        dt = mesh["dt"].squeeze().item()
        source = mesh["source"].squeeze().item()
        aoa = mesh["aoa"].squeeze().item()
        
        try:
            Re=mesh["Re"].squeeze().item()
        except:
            Re=0
            Warning("No Re number in the mesh set to 0")

        if file_name is None:
            save_dir_num = self.plot_count//50
            saving_dir = f"{self.state_save_dir}/{save_dir_num*50}-{(save_dir_num+1)*50}"
            os.makedirs(saving_dir, exist_ok=True)
            saving_path = f"{saving_dir}/NO.{self.plot_count}_{case_name}_Re={Re:.2f}_dt={dt:.3f}_source={source:.2f}_aoa={aoa:.2f}"
        else:
            saving_path = file_name

        export_full_mesh_vtu(
            mesh_pos=mesh_pos.cpu(), # [N, D_pos]
            pv_cells_node=mesh["pv_cells_node"],
            pv_cells_type=mesh["pv_cells_type"],
            data_dict={
                f"cell|Velocity":uvwp[:,0:3].cpu(), # [num_elements]
                f"cell|Pressure":uvwp[:,3:4].cpu(), # [num_elements]
            },
            save_file_path=f"{saving_path}.vtu",
        )

        self.plot_count+=1

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] Exported training results to: {saving_path}")


class HeteroFVGraph:
    """
    Compatibility wrapper that provides the same interface as original FVGraph.
    """
    
    def __init__(self, hetero_data):
        self._hetero_data = hetero_data
        self._create_compatibility_views()
    
    def _create_compatibility_views(self):
        """Create compatibility views that mimic original graph structure."""
        self.graph_node = self._NodeView(self._hetero_data)
        self.graph_face = self._FaceView(self._hetero_data)
        self.graph_cell = self._CellView(self._hetero_data)
        self.graph_cell_x = self._CellXView(self._hetero_data)
        self.graph_index = self._IndexView(self._hetero_data)
    
    class _NodeView:
        def __init__(self, hetero_data):
            self._hetero_data = hetero_data
            
        @property
        def pos(self):
            return self._hetero_data['node'].pos
            
        @property
        def num_nodes(self):
            return self._hetero_data['node'].num_nodes
            
        @property
        def face_node(self):
            return self._hetero_data['face', 'contains', 'node'].face_node
            
        @property
        def cells_node(self):
            return self._hetero_data['cell', 'contains', 'node'].cells_node

        @property
        def _num_nodes(self):
            return self._hetero_data['node'].num_nodes

        @property
        def batch(self):
            return getattr(self._hetero_data['node'], 'batch', None)
    
    class _FaceView:
        def __init__(self, hetero_data):
            self._hetero_data = hetero_data
            
        @property
        def pos(self):
            return self._hetero_data['face'].pos
            
        @property
        def face_area(self):
            return self._hetero_data['face'].face_area
            
        @property
        def face_type(self):
            return self._hetero_data['face'].face_type
        
        @property
        def cyclic_face(self):
            return self._hetero_data['face'].cyclic_face
        
        @property
        def cells_face(self):
            return self._hetero_data['cell', 'bounded_by', 'face'].cells_face
            
        @property
        def face_node_ptr(self):
            return self._hetero_data['face', 'bounds', 'cell'].face_node_ptr

        @property
        def _num_faces(self):
            return self._hetero_data['face'].num_nodes

        @property
        def batch(self):
            return getattr(self._hetero_data['face'], 'batch', None)
    
    class _CellView:
        def __init__(self, hetero_data):
            self._hetero_data = hetero_data
            
        @property
        def x(self):
            return self._hetero_data['cpd_cell'].x
            
        @x.setter
        def x(self, value):
            self._hetero_data['cpd_cell'].x = value
            
        @property
        def y(self):
            return self._hetero_data['cpd_cell'].y
            
        @property
        def pos(self):
            return self._hetero_data['cpd_cell'].pos

        @property
        def cpd_neighbor_cell(self):
            return self._hetero_data['cpd_cell', 'neighbors', 'cpd_cell'].cpd_neighbor_cell

        @property
        def cpd_neighbor_cell_non_cyclic(self):
            return self._hetero_data['cpd_cell', 'neighbors', 'cpd_cell'].cpd_neighbor_cell_non_cyclic
        
        @property
        def owner(self):
            return self._hetero_data['cpd_cell', 'neighbors', 'cpd_cell'].cpd_neighbor_cell[0]
        
        @property
        def cells_volume(self):
            return self._hetero_data['cell'].cells_volume
            
        @property
        def cell_type(self):
            return self._hetero_data['cpd_cell'].cell_type
            
        @property
        def global_idx(self):
            return self._hetero_data['cpd_cell'].global_idx
            
        @property
        def cells_face_unv(self):
            return self._hetero_data['cell'].cells_face_unv

        @property
        def cpd_centroid(self):
            return self._hetero_data['cpd_cell'].pos

        @property
        def cells_node_ptr(self):
            return self._hetero_data['cell', 'contains', 'node'].cells_node_ptr

        @property
        def cells_face_ptr(self):
            return self._hetero_data['cell', 'bounded_by', 'face'].cells_face_ptr

        @property
        def num_graphs(self):
            # Get number of graphs from batch
            batch = getattr(self._hetero_data['cpd_cell'], 'batch', None)
            if batch is not None:
                return batch.max().item() + 1
            else:
                return 1

        @property
        def _num_cells(self):
            return self._hetero_data['cell'].num_nodes

        @property
        def _num_cpd_cells(self):
            return self._hetero_data['cpd_cell'].num_nodes

        @property
        def batch(self):
            return getattr(self._hetero_data['cpd_cell'], 'batch', None)
            
        # Norm attributes for compatibility
        @property
        def norm_uvwp(self):
            return getattr(self._hetero_data['cpd_cell'], 'norm_uvwp', None)
            
        @norm_uvwp.setter
        def norm_uvwp(self, value):
            self._hetero_data['cpd_cell'].norm_uvwp = value
            
        @property
        def norm_global(self):
            return getattr(self._hetero_data['cpd_cell'], 'norm_global', None)
            
        @norm_global.setter
        def norm_global(self, value):
            self._hetero_data['cpd_cell'].norm_global = value
    
    class _CellXView:
        def __init__(self, hetero_data):
            self._hetero_data = hetero_data
            
        @property
        def cpd_neighbor_cell_x(self):
            return self._hetero_data['cpd_cell', 'wlsq_neighbors', 'cpd_cell'].cpd_neighbor_cell_x
            
        @property
        def A_cell_to_cell(self):
            return self._hetero_data['cpd_cell'].A_cell_to_cell
            
        @property
        def single_B_cell_to_cell(self):
            return self._hetero_data['cpd_cell'].single_B_cell_to_cell
            
        @property
        def batch(self):
            return getattr(self._hetero_data['cpd_cell'], 'batch', None)
    
    class _IndexView:
        def __init__(self, hetero_data):
            self._hetero_data = hetero_data
            
        @property
        def theta_PDE(self):
            return self._hetero_data['graph'].theta_PDE
            
        @property
        def uvwp_dim(self):
            return self._hetero_data['graph'].uvwp_dim
            
        @property
        def dt_graph(self):
            return self._hetero_data['graph'].dt_graph
            
        @property
        def case_name(self):
            return self._hetero_data['graph'].case_name
            
        @property
        def graph_index(self):
            return self._hetero_data['graph'].graph_index
            
        @property
        def batch(self):
            return getattr(self._hetero_data['graph'], 'batch', None)
    
    def to(self, *args, exclude_keys=None, **kwargs):
        """Move the underlying hetero data to device/dtype."""
        self._hetero_data = self._hetero_data.to(*args, exclude_keys=exclude_keys, **kwargs)
        self._create_compatibility_views()
        return self
    
    def cuda(self, device=None, exclude_keys=None):
        """Move the underlying hetero data to CUDA device."""
        return self.to(device or 'cuda', exclude_keys=exclude_keys)


def hetero_collate_fn(batch):
    """Custom collate function for heterogeneous graphs."""
    return HeteroBatch.from_data_list(batch)

class HeteroCompatibilityLoader:
    """Optimized compatibility loader that returns HeteroFVGraph objects without DataLoader overhead."""

    def __init__(self, base_dataset, batch_size=1, num_workers=0, pin_memory=True, shuffle=True):
        self.base_dataset = base_dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.pin_memory = pin_memory

        # 创建HeteroGraphDataset用于数据转换
        self.hetero_dataset = HeteroGraphDataset(base_dataset)

        # 预计算数据集大小和索引
        self.dataset_size = len(self.base_dataset.meta_pool)
        self.indices = list(range(self.dataset_size))

        print(f"✅ Optimized loader initialized: {self.dataset_size} samples, batch_size={batch_size}")
        print(f"   - Direct memory access (no DataLoader overhead)")
        print(f"   - Shuffle: {shuffle}, Pin memory: {pin_memory}")

    def __iter__(self):
        """Returns an iterator over HeteroFVGraph objects using direct memory access."""
        # 每个epoch重新shuffle索引
        if self.shuffle:
            import random
            indices = self.indices.copy()
            random.shuffle(indices)
        else:
            indices = self.indices

        # 按batch_size分组生成batch
        for i in range(0, len(indices), self.batch_size):
            batch_indices = indices[i:i + self.batch_size]

            # 直接从内存获取数据，避免DataLoader开销
            batch_data = []
            for idx in batch_indices:
                hetero_data = self.hetero_dataset[idx]
                batch_data.append(hetero_data)

            # 使用原有的collate_fn逻辑
            hetero_batch = hetero_collate_fn(batch_data)

            # 返回兼容的HeteroFVGraph对象
            yield HeteroFVGraph(hetero_batch)

    def __len__(self):
        """返回batch数量"""
        return (self.dataset_size + self.batch_size - 1) // self.batch_size

    def __getitem__(self, indices):
        """Support direct indexing: loader[indices] - returns HeteroFVGraph"""
        hetero_graph = self.hetero_dataset[indices]
        return HeteroFVGraph(hetero_graph)


class HeteroDatasetFactory:
    """Factory class to create heterogeneous graph datasets and loaders."""
    
    def __init__(self, params=None, dataset_dir=None, state_save_dir=None, device=None):
        self.base_dataset = HeteroDataPool(
            params=params,
            device=device,
            state_save_dir=state_save_dir,
        )

        self.params = self.base_dataset.load_mesh_to_cpu(
            dataset_dir=dataset_dir,
        )

    def create_loader(self, batch_size=1, num_workers=0, pin_memory=True, shuffle=True):
        """Creates compatibility loader (default for backward compatibility)."""
        compatibility_loader = HeteroCompatibilityLoader(
            base_dataset=self.base_dataset,
            batch_size=batch_size,
            num_workers=num_workers,
            pin_memory=pin_memory,
            shuffle=shuffle
        )
        return self.base_dataset, compatibility_loader
