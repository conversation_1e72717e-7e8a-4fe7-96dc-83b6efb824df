# Gen-FVGN: Learning to Solve 3D PDEs with Finite Volume-Informed Neural Networks
Partial differential equations (PDEs) play a crucial role in scientific computing. 
Recent advancements in deep learning have led to the development of both data-driven and and Physics-Informed Neural Networks (PINNs) for efficiently solving PDEs, though challenges remain in data acquisition and generalization for both approaches.
This paper presents a computational framework that combines the Finite Volume Method (FVM) with Graph Neural Networks (GNNs) to construct the PDE-loss, enabling direct parametric PDE solving on **3D unstructured meshes** during training without the need for precomputed data.
By exploiting GNNs' flexibility on unstructured grids, 
this framework extends its applicability across various geometries, physical equations and boundary conditions. 
The core innovation lies in an unsupervised training algorithm that utilizes GPU parallel computing to create a fully differentiable finite volume discretization process, 
such as gradient reconstruction and surface integration.
Our results demonstrate that the trained GNN model can efficiently solve multiple PDEs with varying boundary conditions and source terms in a single training session, 
with the number of iterations required to reach a steady-state solution being only 25\% of that required by traditional second-order CFD solvers.
[[https](https://doi.org/10.1016/j.jcp.2025.113919)][[arxiv](https://arxiv.org/pdf/2405.04466)]

> Update (14.06.2025): This repository now fully supports **3D unstructured meshes** in **OpenFOAM format**. Support for COMSOL and Tecplot mesh formats has been discontinued. The workflow has been updated to reflect these changes.

# Catalog

- [Gen-FVGN: Learning to Solve 3D PDEs with Finite Volume-Informed Neural Networks](#gen-fvgn-learning-to-solve-3d-pdes-with-finite-volume-informed-neural-networks)
- [Catalog](#catalog)
- [Installation of the Code Environment](#installation-of-the-code-environment)
- [How To Use (Updated Workflow)](#how-to-use-updated-workflow)
  - [Step 1: Mesh Preparation (OpenFOAM)](#step-1-mesh-preparation-openfoam)
  - [Step 2: Pre-Training](#step-2-pre-training)
  - [Step 3: Inference](#step-3-inference)
    - [Inference Without Adam (Rollout)](#inference-without-adam-rollout)
    - [Inference With Adam (PINN-Style)](#inference-with-adam-pinn-style)
- [Code Directory and File Structure](#code-directory-and-file-structure)
    - [Instructions for Mesh Directories](#instructions-for-mesh-directories)
    - [Parameter Configuration](#parameter-configuration)
  - [Common Issues](#common-issues)
    - [Loss Calculation Produces NaN](#loss-calculation-produces-nan)

# Installation of the Code Environment

**pytorch-2.7.0**
```bash
conda create -n FVGN-pt2.7 python==3.11 # Create a new conda environment and specify the Python version
conda activate FVGN-pt2.7 # Activate the Python environment
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128 # Install the GPU-enabled PyTorch 2.7 version
pip install torch_geometric -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install scipy
pip install --no-index pyg_lib torch_scatter torch_sparse torch_cluster torch_spline_conv -f https://pytorch-geometric.com/whl/torch-2.7.0+cu128.html
pip install -r src/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

**pytorch-2.5.1**
```bash
conda create -n FVGN-pt2.5 python==3.11 # Create a new conda environment and specify the Python version
conda activate FVGN-pt2.5 # Activate the Python environment
conda install pytorch torchvision torchaudio pytorch-cuda=12.4 -c pytorch -c nvidia # Install the GPU-enabled PyTorch 2.5.1 version
pip install scipy -i https://pypi.tuna.tsinghua.edu.cn/simple # Pre-install scipy to avoid potential conflicts later
pip install sympy==1.13.1 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install torch_geometric -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install --no-index pyg_lib torch_scatter torch_sparse torch_cluster torch_spline_conv -f https://pytorch-geometric.com/whl/torch-2.5.1+cu124.html
pip install -r src/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

# How To Use (Updated Workflow)

Gen-FVGN uses a custom `.h5` data format. The workflow begins by converting a standard OpenFOAM case into this format.

## Step 1: Mesh Preparation (OpenFOAM)

The model now exclusively supports **3D unstructured meshes** from **OpenFOAM**.

1.  **Prepare an OpenFOAM case directory.** A sample case, `lidDrivenCavity3D-simple`, is provided in the `mesh_example/` directory. Your case directory must contain the standard OpenFOAM structure, including `constant/polyMesh` and the initial condition files in the `0/` directory (e.g., `U`, `p`).

2.  **Convert the mesh to `.h5` format.** Open the `src/Parse_mesh/parse_openfoam_refactored.py` script and set the `case_path` variable to point to your OpenFOAM case directory.

    ```python
    # In src/Parse_mesh/parse_openfoam_refactored.py
    case_path = 'mesh_example/lidDrivenCavity3D-simple' 
    ```

3.  **Run the conversion script:**
    ```bash
    python src/Parse_mesh/parse_openfoam_refactored.py
    ```
    This will process the OpenFOAM mesh and boundary conditions, generating a single `data.h5` file inside your case directory. This `.h5` file contains all the necessary geometric and boundary information for the model.

## Step 2: Pre-Training
This step trains the GNN model to solve the PDE defined by your mesh and parameters.

1.  **Configure parameters.** Open `src/Utils/get_param.py` to adjust training parameters. The most important parameter is `--dataset_dir`, which must point to the directory containing your `data.h5` file.

    ```python
    # In src/pre_train_Adam.py or src/Utils/get_param.py
    params.dataset_dir = "mesh_example/lidDrivenCavity3D-simple"
    ```
    You can also adjust other parameters like learning rate (`--lr`), batch size (`--batch_size`), and the number of epochs (`--n_epochs`).

2.  **Start training:**
    ```bash
    python src/pre_train_Adam.py
    ```
    The training progress, logs, and model checkpoints will be saved in the `Logger/` directory.

## Step 3: Inference
After training, you can use the saved model to perform inference and solve for the flow field.

### Inference Without Adam (Rollout)
This method uses the trained model directly for rapid prediction.

1.  **Configure the inference script.** Open `src/solve_without_grad_GPU.py` and set the following parameters:
    *   `params.load_date_time`: The timestamp of the trained model you want to use (e.g., `"2025-06-14-..."`). This corresponds to a folder in the `Logger/` directory.
    *   `params.load_index`: The model checkpoint index to load (e.g., `1`).
    *   `params.dataset_dir`: The path to the mesh directory for inference.

2.  **Run inference:**
    ```bash
    python src/inference_without_adam.py
    ```

### Inference With Adam (PINN-Style) (Not implemented)
This method fine-tunes the model during inference, which can improve accuracy but is slower.

1.  **Configure the inference script.** Open `src/inference_without_adam.py`. Set the `load_date_time`, `load_index`, and `dataset_dir` parameters as in the previous method. You must also set `params.max_inner_steps` to define the number of optimization steps.

2.  **Run inference:**
    ```bash
    python src/inference_with_adam.py
    ```
The results of the inference will be saved as Tecplot-compatible files in the mesh directory.

# Code Directory and File Structure

- **datasets/**: A directory to store your mesh files. This path is included in `.gitignore`, so local files won't be tracked by git. You may need to create this folder.
- **mesh_example/**: Contains an example OpenFOAM case (`lidDrivenCavity3D-simple`) to demonstrate the expected structure.
- **Logger/**: Records files generated during training, including model checkpoints, logs, and solution files.
- **src/**: The core source code directory, containing model implementations, training/inference scripts, and mesh processing tools.

### Instructions for Mesh Directories
The path to the mesh directory is a critical parameter. It must be set correctly in the training and inference scripts via the `--dataset_dir` argument.

An example OpenFOAM case is located at `mesh_example/lidDrivenCavity3D-simple`. After running the `src/Parse_mesh/parse_openfoam_refactored.py` script, the directory will contain the generated `data.h5` file alongside the original OpenFOAM files:
```
--- lidDrivenCavity3D-simple/
    |-- 0/
    |   |-- p
    |   `-- U
    |-- constant/
    |   `-- polyMesh/
    |       |-- boundary
    |       |-- faces
    |       |-- neighbour
    |       |-- owner
    |       `-- points
    |-- system/
    |   |-- blockMeshDict
    |   |-- controlDict
    |   |-- fvSchemes
    |   `-- fvSolution
    `-- h5  <-- Generated by the script
    `-- log  <-- Generated by the script
```

### Parameter Configuration
Most training and model parameters are managed through command-line arguments defined in `src/Utils/get_param.py`. You can modify the default values in this file or change the parameters directly in the main scripts (`pre_train_Adam.py`, `inference_without_adam.py`, etc.).

The `BC.json` file is no longer used for defining boundary conditions or geometric entities, as this information is now parsed directly from the OpenFOAM case files.

## Common Issues
### Loss Calculation Produces NaN
Please check if the `norm_global` parameter is enabled in `src/Utils/get_param.py`. If it is enabled, ensure that at least one of the physical parameters (like Reynolds number, derived from velocity, viscosity, etc.) has a valid range where the start and end values are not identical. When `norm_global` is enabled, normalization is applied to these parameters, and if all values in a range are the same, it can lead to division by zero during normalization.
